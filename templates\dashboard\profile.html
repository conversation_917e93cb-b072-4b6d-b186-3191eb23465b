{% extends "dashboard/base.html" %}

{% block title %}Profil User{% endblock %}
{% block page_title %}Profil User{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <!-- User Profile Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    Informasi Profil
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center">
                        <div class="mb-3">
                            <i class="fas fa-user-circle fa-5x text-primary"></i>
                        </div>
                        <h5>{{ current_user.full_name }}</h5>
                        <p class="text-muted">{{ current_user.role.title() }}</p>
                        <span class="badge bg-{{ 'success' if current_user.is_active else 'danger' }} fs-6">
                            {{ 'Aktif' if current_user.is_active else 'Tidak Aktif' }}
                        </span>
                    </div>
                    
                    <div class="col-md-8">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Username:</strong></td>
                                <td>{{ current_user.username }}</td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td>{{ current_user.email }}</td>
                            </tr>
                            <tr>
                                <td><strong>Nama Lengkap:</strong></td>
                                <td>{{ current_user.full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>Role:</strong></td>
                                <td>
                                    <span class="badge bg-primary">{{ current_user.role.title() }}</span>
                                    {% if current_user.is_admin() %}
                                        <small class="text-muted">(Full Access)</small>
                                    {% elif current_user.is_manager() %}
                                        <small class="text-muted">(Manager Access)</small>
                                    {% else %}
                                        <small class="text-muted">(User Access)</small>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td>
                                    <span class="badge bg-{{ 'success' if current_user.is_active else 'danger' }}">
                                        {{ 'Aktif' if current_user.is_active else 'Tidak Aktif' }}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Role Permissions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    Hak Akses & Permissions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">✅ Yang Dapat Anda Lakukan:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Melihat Dashboard</li>
                            <li><i class="fas fa-check text-success me-2"></i>Membuat Prediksi Kredit</li>
                            <li><i class="fas fa-check text-success me-2"></i>Melihat Hasil Prediksi</li>
                            <li><i class="fas fa-check text-success me-2"></i>Mengubah Profil</li>
                            <li><i class="fas fa-check text-success me-2"></i>Mengubah Password</li>
                            
                            {% if current_user.is_manager() %}
                            <li><i class="fas fa-check text-success me-2"></i>Training Model Neural Network</li>
                            <li><i class="fas fa-check text-success me-2"></i>Melihat Statistik Lanjutan</li>
                            {% endif %}
                            
                            {% if current_user.is_admin() %}
                            <li><i class="fas fa-check text-success me-2"></i>Mengelola User</li>
                            <li><i class="fas fa-check text-success me-2"></i>Melihat Analytics</li>
                            <li><i class="fas fa-check text-success me-2"></i>Mendaftarkan User Baru</li>
                            <li><i class="fas fa-check text-success me-2"></i>Mengubah Role User</li>
                            {% endif %}
                        </ul>
                    </div>
                    
                    <div class="col-md-6">
                        {% if not current_user.is_admin() %}
                        <h6 class="text-muted">❌ Yang Tidak Dapat Anda Lakukan:</h6>
                        <ul class="list-unstyled">
                            {% if not current_user.is_manager() %}
                            <li><i class="fas fa-times text-danger me-2"></i>Training Model Neural Network</li>
                            <li><i class="fas fa-times text-danger me-2"></i>Melihat Statistik User</li>
                            {% endif %}
                            <li><i class="fas fa-times text-danger me-2"></i>Mengelola User Lain</li>
                            <li><i class="fas fa-times text-danger me-2"></i>Mengubah Role User</li>
                            <li><i class="fas fa-times text-danger me-2"></i>Melihat Analytics Admin</li>
                            <li><i class="fas fa-times text-danger me-2"></i>Mendaftarkan User Baru</li>
                        </ul>
                        {% else %}
                        <h6 class="text-success">🔓 Administrator Access</h6>
                        <p class="text-muted">Anda memiliki akses penuh ke semua fitur sistem.</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-2">
                        <a href="{{ url_for('change_password') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-key me-2"></i>
                            Ubah Password
                        </a>
                    </div>
                    <div class="col-md-6 mb-2">
                        <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            Kembali ke Dashboard
                        </a>
                    </div>
                    <div class="col-md-6 mb-2">
                        <a href="{{ url_for('predict') }}" class="btn btn-outline-success w-100">
                            <i class="fas fa-calculator me-2"></i>
                            Buat Prediksi Baru
                        </a>
                    </div>
                    <div class="col-md-6 mb-2">
                        <a href="{{ url_for('logout') }}" class="btn btn-outline-danger w-100">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Information -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Informasi Sistem
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>🧠 Neural Network</h6>
                        <ul class="small list-unstyled">
                            <li><i class="fas fa-check text-success me-1"></i> Algoritma: Backpropagation</li>
                            <li><i class="fas fa-check text-success me-1"></i> Input Features: 11</li>
                            <li><i class="fas fa-check text-success me-1"></i> Hidden Layers: 2</li>
                            <li><i class="fas fa-check text-success me-1"></i> Activation: Sigmoid</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>💾 Database</h6>
                        <ul class="small list-unstyled">
                            <li><i class="fas fa-check text-success me-1"></i> Type: SQLite</li>
                            <li><i class="fas fa-check text-success me-1"></i> Auto Backup: Enabled</li>
                            <li><i class="fas fa-check text-success me-1"></i> Encryption: Active</li>
                            <li><i class="fas fa-check text-success me-1"></i> Real-time Sync: On</li>
                        </ul>
                    </div>
                </div>
                
                <div class="mt-3 p-3 bg-light rounded">
                    <small class="text-muted">
                        <i class="fas fa-shield-alt me-1"></i>
                        <strong>Keamanan:</strong> Semua aktivitas Anda dicatat dalam sistem log untuk keperluan audit dan keamanan.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
