{% extends "base.html" %}

{% block title %}Prediksi Kelayakan Kredit{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-calculator"></i> Form Prediksi Kelayakan Kredit
                </h4>
            </div>
            <div class="card-body">
                {% if error %}
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle"></i> {{ error }}
                </div>
                {% endif %}

                <form method="POST" id="predictForm">
                    <!-- Data Pribadi -->
                    <h5 class="text-primary mb-3">
                        <i class="fas fa-user"></i> Data Pribadi
                    </h5>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="nama" class="form-label"><PERSON><PERSON>g<PERSON></label>
                            <input type="text" class="form-control" id="nama" name="nama" required>
                        </div>
                        <div class="col-md-6">
                            <label for="umur" class="form-label">Umur (tahun)</label>
                            <input type="number" class="form-control" id="umur" name="umur" min="18" max="70" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="status_pernikahan" class="form-label">Status Pernikahan</label>
                            <select class="form-select" id="status_pernikahan" name="status_pernikahan" required>
                                <option value="">Pilih Status</option>
                                <option value="0">Belum Menikah</option>
                                <option value="1">Sudah Menikah</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="jumlah_tanggungan" class="form-label">Jumlah Tanggungan</label>
                            <input type="number" class="form-control" id="jumlah_tanggungan" name="jumlah_tanggungan" min="0" max="10" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="pendidikan" class="form-label">Tingkat Pendidikan</label>
                            <select class="form-select" id="pendidikan" name="pendidikan" required>
                                <option value="">Pilih Pendidikan</option>
                                <option value="0">SD</option>
                                <option value="1">SMP</option>
                                <option value="2">SMA</option>
                                <option value="3">S1</option>
                                <option value="4">S2/S3</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="kepemilikan_rumah" class="form-label">Kepemilikan Rumah</label>
                            <select class="form-select" id="kepemilikan_rumah" name="kepemilikan_rumah" required>
                                <option value="">Pilih Status</option>
                                <option value="0">Sewa/Kontrak</option>
                                <option value="1">Milik Sendiri</option>
                            </select>
                        </div>
                    </div>

                    <hr>

                    <!-- Data Pekerjaan -->
                    <h5 class="text-primary mb-3">
                        <i class="fas fa-briefcase"></i> Data Pekerjaan
                    </h5>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="pekerjaan" class="form-label">Status Pekerjaan</label>
                            <select class="form-select" id="pekerjaan" name="pekerjaan" required>
                                <option value="">Pilih Status</option>
                                <option value="0">Tidak Tetap</option>
                                <option value="1">Tetap</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="lama_bekerja" class="form-label">Lama Bekerja (tahun)</label>
                            <input type="number" class="form-control" id="lama_bekerja" name="lama_bekerja" min="0" max="50" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="pendapatan_bulanan" class="form-label">Pendapatan Bulanan (Rp)</label>
                            <input type="number" class="form-control" id="pendapatan_bulanan" name="pendapatan_bulanan" min="1000000" step="100000" required>
                            <div class="form-text">Masukkan pendapatan bulanan dalam Rupiah</div>
                        </div>
                    </div>

                    <hr>

                    <!-- Data Kredit -->
                    <h5 class="text-primary mb-3">
                        <i class="fas fa-credit-card"></i> Data Kredit
                    </h5>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="jumlah_kredit" class="form-label">Jumlah Kredit yang Diajukan (Rp)</label>
                            <input type="number" class="form-control" id="jumlah_kredit" name="jumlah_kredit" min="1000000" step="100000" required>
                        </div>
                        <div class="col-md-6">
                            <label for="jangka_waktu" class="form-label">Jangka Waktu (bulan)</label>
                            <select class="form-select" id="jangka_waktu" name="jangka_waktu" required>
                                <option value="">Pilih Jangka Waktu</option>
                                <option value="12">12 bulan</option>
                                <option value="24">24 bulan</option>
                                <option value="36">36 bulan</option>
                                <option value="48">48 bulan</option>
                                <option value="60">60 bulan</option>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="riwayat_kredit" class="form-label">Riwayat Kredit</label>
                            <select class="form-select" id="riwayat_kredit" name="riwayat_kredit" required>
                                <option value="">Pilih Riwayat</option>
                                <option value="0">Buruk (pernah menunggak/macet)</option>
                                <option value="1">Baik (tidak pernah bermasalah)</option>
                            </select>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-calculator"></i> Prediksi Kelayakan
                        </button>
                        <a href="{{ url_for('index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Info Panel -->
<div class="row mt-4">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> Informasi Penting
                </h5>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li>Pastikan semua data yang dimasukkan akurat dan sesuai dengan kondisi sebenarnya</li>
                    <li>Sistem akan menganalisis 11 parameter untuk memberikan prediksi yang optimal</li>
                    <li>Hasil prediksi bersifat rekomendasi dan dapat digunakan sebagai bahan pertimbangan</li>
                    <li>Keputusan akhir tetap berada di tangan pihak yang berwenang</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.getElementById('predictForm').addEventListener('submit', function(e) {
    // Validasi tambahan
    const pendapatan = parseInt(document.getElementById('pendapatan_bulanan').value);
    const kredit = parseInt(document.getElementById('jumlah_kredit').value);
    
    if (kredit > pendapatan * 60) { // 5 tahun
        if (!confirm('Jumlah kredit sangat besar dibanding pendapatan. Yakin ingin melanjutkan?')) {
            e.preventDefault();
        }
    }
});

// Format input rupiah
document.getElementById('pendapatan_bulanan').addEventListener('input', function(e) {
    // Format number dengan separator
    let value = e.target.value.replace(/\D/g, '');
    e.target.value = value;
});

document.getElementById('jumlah_kredit').addEventListener('input', function(e) {
    // Format number dengan separator
    let value = e.target.value.replace(/\D/g, '');
    e.target.value = value;
});
</script>
{% endblock %}
