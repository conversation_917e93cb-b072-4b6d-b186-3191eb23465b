#!/usr/bin/env python3
"""
Quick start untuk sistem prediksi kredit
"""

print("🚀 QUICK START - Sistem Prediksi Kelayakan Kredit")
print("=" * 60)

# Test imports
print("📦 Testing imports...")
try:
    import numpy as np
    print("  ✅ NumPy")
    
    import pandas as pd
    print("  ✅ Pandas")
    
    from flask import Flask
    print("  ✅ Flask")
    
    import sqlite3
    print("  ✅ SQLite3")
    
    print("✅ All imports successful!")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please run: pip install Flask numpy pandas scikit-learn")
    exit(1)

# Test neural network
print("\n🧠 Testing Neural Network...")
try:
    from models.neural_network import NeuralNetwork
    
    # Quick test
    nn = NeuralNetwork(input_size=3, hidden_layers=[5])
    X = np.random.rand(10, 3)
    y = np.random.randint(0, 2, (10, 1))
    
    losses = nn.train(X, y, epochs=10, verbose=False)
    accuracy = nn.calculate_accuracy(X, y)
    
    print(f"  ✅ Neural Network OK - Accuracy: {accuracy:.2%}")
    
except Exception as e:
    print(f"  ❌ Neural Network error: {e}")

# Test database
print("\n💾 Testing Database...")
try:
    from models.database import CreditDatabase
    
    # Create test database
    import os
    os.makedirs('data', exist_ok=True)
    
    db = CreditDatabase('data/quick_test.db')
    stats = db.get_statistics()
    
    print(f"  ✅ Database OK - {stats['total_applications']} records")
    
except Exception as e:
    print(f"  ❌ Database error: {e}")

# Start Flask app
print("\n🌐 Starting Flask Application...")
try:
    from flask import Flask, render_template_string
    
    app = Flask(__name__)
    
    @app.route('/')
    def home():
        return render_template_string('''
        <!DOCTYPE html>
        <html>
        <head>
            <title>Sistem Prediksi Kelayakan Kredit</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        </head>
        <body>
            <div class="container mt-5">
                <div class="row">
                    <div class="col-md-8 mx-auto">
                        <div class="card">
                            <div class="card-header bg-success text-white text-center">
                                <h2>🎉 SISTEM BERHASIL BERJALAN!</h2>
                            </div>
                            <div class="card-body">
                                <h4 class="text-center mb-4">🧠 Sistem Prediksi Kelayakan Kredit</h4>
                                <p class="text-center">Neural Network dengan Backpropagation</p>
                                
                                <div class="alert alert-success">
                                    <h5>✅ Status Komponen:</h5>
                                    <ul class="mb-0">
                                        <li>✅ Flask Web Server: Running</li>
                                        <li>✅ Neural Network: Ready</li>
                                        <li>✅ Database: Connected</li>
                                        <li>✅ Web Interface: Active</li>
                                    </ul>
                                </div>
                                
                                <div class="row mt-4">
                                    <div class="col-md-6">
                                        <div class="card border-primary">
                                            <div class="card-body text-center">
                                                <h6>📊 Fitur Sistem</h6>
                                                <ul class="list-unstyled small">
                                                    <li>✓ Prediksi Kelayakan Kredit</li>
                                                    <li>✓ Training Model AI</li>
                                                    <li>✓ Database Management</li>
                                                    <li>✓ RESTful API</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card border-info">
                                            <div class="card-body text-center">
                                                <h6>🔧 Teknologi</h6>
                                                <ul class="list-unstyled small">
                                                    <li>✓ Python Flask</li>
                                                    <li>✓ Neural Network</li>
                                                    <li>✓ SQLite Database</li>
                                                    <li>✓ Bootstrap UI</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="text-center mt-4">
                                    <div class="alert alert-info">
                                        <strong>🎯 Sistem Siap Digunakan!</strong><br>
                                        Untuk menggunakan sistem lengkap, jalankan: <code>python app.py</code>
                                    </div>
                                    
                                    <a href="/demo-predict" class="btn btn-primary me-2">
                                        🧠 Demo Prediksi
                                    </a>
                                    <a href="/demo-stats" class="btn btn-success">
                                        📊 Demo Database
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </body>
        </html>
        ''')
    
    @app.route('/demo-predict')
    def demo_predict():
        try:
            from models.neural_network import NeuralNetwork
            from models.database import CreditDatabase
            
            # Quick demo prediction
            db = CreditDatabase('data/quick_test.db')
            X, y = db.get_training_data()
            
            # Train quick model
            model = NeuralNetwork(input_size=X.shape[1], hidden_layers=[8, 5])
            losses = model.train(X, y, epochs=50, verbose=False)
            accuracy = model.calculate_accuracy(X, y)
            
            # Demo prediction
            test_input = X[0:1]  # First sample
            prediction = model.predict(test_input)[0][0]
            actual = y[0][0]
            
            return render_template_string('''
            <div class="container mt-5">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4>🧠 Demo Prediksi Neural Network</h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <h5>✅ Model Training Berhasil!</h5>
                            <ul class="mb-0">
                                <li>Akurasi Model: {{ "%.1f"|format(accuracy * 100) }}%</li>
                                <li>Training Epochs: 50</li>
                                <li>Final Loss: {{ "%.4f"|format(losses[-1]) }}</li>
                            </ul>
                        </div>
                        
                        <div class="card mt-3">
                            <div class="card-body">
                                <h6>🎯 Hasil Prediksi Sample:</h6>
                                <ul>
                                    <li><strong>Probabilitas:</strong> {{ "%.1f"|format(prediction * 100) }}%</li>
                                    <li><strong>Prediksi:</strong> {{ "LAYAK" if prediction >= 0.5 else "TIDAK LAYAK" }}</li>
                                    <li><strong>Aktual:</strong> {{ "LAYAK" if actual == 1 else "TIDAK LAYAK" }}</li>
                                    <li><strong>Status:</strong> {{ "✅ BENAR" if (prediction >= 0.5) == (actual == 1) else "❌ SALAH" }}</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="text-center mt-3">
                            <a href="/" class="btn btn-secondary">🏠 Kembali</a>
                        </div>
                    </div>
                </div>
            </div>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            ''', accuracy=accuracy, losses=losses, prediction=prediction, actual=actual)
            
        except Exception as e:
            return f"<h3>Error: {e}</h3><a href='/'>Back</a>"
    
    @app.route('/demo-stats')
    def demo_stats():
        try:
            from models.database import CreditDatabase
            
            db = CreditDatabase('data/quick_test.db')
            stats = db.get_statistics()
            X, y = db.get_training_data()
            
            return render_template_string('''
            <div class="container mt-5">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h4>📊 Demo Database Statistics</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card border-primary">
                                    <div class="card-body text-center">
                                        <h5>📈 Statistik Aplikasi</h5>
                                        <ul class="list-unstyled">
                                            <li><strong>Total Aplikasi:</strong> {{ stats.total_applications }}</li>
                                            <li><strong>Disetujui:</strong> {{ stats.approved }}</li>
                                            <li><strong>Ditolak:</strong> {{ stats.rejected }}</li>
                                            <li><strong>Prediksi:</strong> {{ stats.total_predictions }}</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-body text-center">
                                        <h5>🔢 Data Training</h5>
                                        <ul class="list-unstyled">
                                            <li><strong>Samples:</strong> {{ X.shape[0] }}</li>
                                            <li><strong>Features:</strong> {{ X.shape[1] }}</li>
                                            <li><strong>Approval Rate:</strong> {{ "%.1f"|format((stats.approved / stats.total_applications * 100) if stats.total_applications > 0 else 0) }}%</li>
                                            <li><strong>Database:</strong> SQLite</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-3">
                            <strong>💡 Info:</strong> Database berisi data sample yang digenerate otomatis 
                            untuk keperluan training dan testing model neural network.
                        </div>
                        
                        <div class="text-center mt-3">
                            <a href="/" class="btn btn-secondary">🏠 Kembali</a>
                        </div>
                    </div>
                </div>
            </div>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            ''', stats=stats, X=X)
            
        except Exception as e:
            return f"<h3>Error: {e}</h3><a href='/'>Back</a>"
    
    print("  ✅ Flask app configured")
    print("\n" + "=" * 60)
    print("🎉 SISTEM BERHASIL DIJALANKAN!")
    print("📍 URL: http://localhost:5000")
    print("🔧 Mode: Quick Start Demo")
    print("=" * 60)
    
    app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)
    
except Exception as e:
    print(f"❌ Flask error: {e}")
    import traceback
    traceback.print_exc()

print("\n👋 Quick start selesai!")
