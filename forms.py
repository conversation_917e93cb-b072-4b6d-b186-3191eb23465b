from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON><PERSON>, PasswordField, SelectField, IntegerField, FloatField, SubmitField, BooleanField
from wtforms.validators import DataRequired, Email, Length, NumberRange, EqualTo, ValidationError
from models.user import UserManager
from config import Config

class LoginForm(FlaskForm):
    username = StringField('Username', validators=[
        DataRequired(message='Username harus diisi'),
        Length(min=3, max=50, message='Username harus 3-50 karakter')
    ])
    password = PasswordField('Password', validators=[
        DataRequired(message='Password harus diisi'),
        Length(min=3, message='Password minimal 3 karakter')
    ])
    remember_me = BooleanField('Ingat Saya')
    submit = SubmitField('Login')

class RegisterForm(FlaskForm):
    username = <PERSON>Field('Username', validators=[
        DataRequired(message='Username harus diisi'),
        Length(min=3, max=50, message='Username harus 3-50 karakter')
    ])
    email = StringField('Email', validators=[
        DataRequired(message='Email harus diisi'),
        Email(message='Format email tidak valid')
    ])
    full_name = StringField('Nama Lengkap', validators=[
        DataRequired(message='Nama lengkap harus diisi'),
        Length(min=2, max=100, message='Nama harus 2-100 karakter')
    ])
    password = PasswordField('Password', validators=[
        DataRequired(message='Password harus diisi'),
        Length(min=6, message='Password minimal 6 karakter')
    ])
    password2 = PasswordField('Konfirmasi Password', validators=[
        DataRequired(message='Konfirmasi password harus diisi'),
        EqualTo('password', message='Password tidak cocok')
    ])
    role = SelectField('Role', choices=[
        ('user', 'Credit Analyst'),
        ('manager', 'Credit Manager')
    ], default='user')
    submit = SubmitField('Daftar')

    def validate_username(self, username):
        import sqlite3
        try:
            conn = sqlite3.connect(Config.USER_DATABASE_PATH)
            cursor = conn.cursor()
            cursor.execute("SELECT id FROM users WHERE username = ?", [username.data])
            if cursor.fetchone():
                conn.close()
                raise ValidationError('Username sudah digunakan')
            conn.close()
        except:
            pass  # Database belum ada, skip validation

    def validate_email(self, email):
        import sqlite3
        try:
            conn = sqlite3.connect(Config.USER_DATABASE_PATH)
            cursor = conn.cursor()
            cursor.execute("SELECT id FROM users WHERE email = ?", [email.data])
            if cursor.fetchone():
                conn.close()
                raise ValidationError('Email sudah digunakan')
            conn.close()
        except:
            pass  # Database belum ada, skip validation

class PredictionForm(FlaskForm):
    nama = StringField('Nama Lengkap', validators=[
        DataRequired(message='Nama harus diisi'),
        Length(min=2, max=100, message='Nama harus 2-100 karakter')
    ])

    umur = IntegerField('Umur (tahun)', validators=[
        DataRequired(message='Umur harus diisi'),
        NumberRange(min=18, max=70, message='Umur harus antara 18-70 tahun')
    ])

    pendapatan_bulanan = FloatField('Pendapatan Bulanan (Rp)', validators=[
        DataRequired(message='Pendapatan harus diisi'),
        NumberRange(min=1000000, message='Pendapatan minimal Rp 1.000.000')
    ])

    jumlah_tanggungan = IntegerField('Jumlah Tanggungan', validators=[
        DataRequired(message='Jumlah tanggungan harus diisi'),
        NumberRange(min=0, max=10, message='Jumlah tanggungan 0-10 orang')
    ])

    lama_bekerja = IntegerField('Lama Bekerja (tahun)', validators=[
        DataRequired(message='Lama bekerja harus diisi'),
        NumberRange(min=0, max=50, message='Lama bekerja 0-50 tahun')
    ])

    jumlah_kredit = FloatField('Jumlah Kredit (Rp)', validators=[
        DataRequired(message='Jumlah kredit harus diisi'),
        NumberRange(min=1000000, message='Jumlah kredit minimal Rp 1.000.000')
    ])

    jangka_waktu = SelectField('Jangka Waktu', choices=[
        (12, '12 bulan'),
        (24, '24 bulan'),
        (36, '36 bulan'),
        (48, '48 bulan'),
        (60, '60 bulan')
    ], coerce=int, validators=[DataRequired(message='Jangka waktu harus dipilih')])

    riwayat_kredit = SelectField('Riwayat Kredit', choices=[
        (0, 'Buruk (pernah menunggak/macet)'),
        (1, 'Baik (tidak pernah bermasalah)')
    ], coerce=int, validators=[DataRequired(message='Riwayat kredit harus dipilih')])

    status_pernikahan = SelectField('Status Pernikahan', choices=[
        (0, 'Belum Menikah'),
        (1, 'Sudah Menikah')
    ], coerce=int, validators=[DataRequired(message='Status pernikahan harus dipilih')])

    pendidikan = SelectField('Tingkat Pendidikan', choices=[
        (0, 'SD'),
        (1, 'SMP'),
        (2, 'SMA'),
        (3, 'S1'),
        (4, 'S2/S3')
    ], coerce=int, validators=[DataRequired(message='Pendidikan harus dipilih')])

    pekerjaan = SelectField('Status Pekerjaan', choices=[
        (0, 'Tidak Tetap'),
        (1, 'Tetap')
    ], coerce=int, validators=[DataRequired(message='Status pekerjaan harus dipilih')])

    kepemilikan_rumah = SelectField('Kepemilikan Rumah', choices=[
        (0, 'Sewa/Kontrak'),
        (1, 'Milik Sendiri')
    ], coerce=int, validators=[DataRequired(message='Kepemilikan rumah harus dipilih')])

    submit = SubmitField('Prediksi Kelayakan')

class ChangePasswordForm(FlaskForm):
    current_password = PasswordField('Password Saat Ini', validators=[
        DataRequired(message='Password saat ini harus diisi')
    ])
    new_password = PasswordField('Password Baru', validators=[
        DataRequired(message='Password baru harus diisi'),
        Length(min=6, message='Password minimal 6 karakter')
    ])
    new_password2 = PasswordField('Konfirmasi Password Baru', validators=[
        DataRequired(message='Konfirmasi password harus diisi'),
        EqualTo('new_password', message='Password tidak cocok')
    ])
    submit = SubmitField('Ubah Password')

class UserManagementForm(FlaskForm):
    username = StringField('Username', validators=[
        DataRequired(message='Username harus diisi'),
        Length(min=3, max=50, message='Username harus 3-50 karakter')
    ])
    email = StringField('Email', validators=[
        DataRequired(message='Email harus diisi'),
        Email(message='Format email tidak valid')
    ])
    full_name = StringField('Nama Lengkap', validators=[
        DataRequired(message='Nama lengkap harus diisi'),
        Length(min=2, max=100, message='Nama harus 2-100 karakter')
    ])
    role = SelectField('Role', choices=[
        ('user', 'Credit Analyst'),
        ('manager', 'Credit Manager'),
        ('admin', 'Administrator')
    ], validators=[DataRequired(message='Role harus dipilih')])
    is_active = BooleanField('Aktif')
    submit = SubmitField('Simpan')
