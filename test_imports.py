#!/usr/bin/env python3
"""
Test script untuk mengecek import dependencies
"""

print("Testing imports...")

try:
    import numpy as np
    print("✓ NumPy imported successfully")
except ImportError as e:
    print(f"✗ NumPy import failed: {e}")

try:
    import pandas as pd
    print("✓ Pandas imported successfully")
except ImportError as e:
    print(f"✗ Pandas import failed: {e}")

try:
    from flask import Flask
    print("✓ Flask imported successfully")
except ImportError as e:
    print(f"✗ Flask import failed: {e}")

try:
    import sqlite3
    print("✓ SQLite3 imported successfully")
except ImportError as e:
    print(f"✗ SQLite3 import failed: {e}")

try:
    import os
    print("✓ OS imported successfully")
except ImportError as e:
    print(f"✗ OS import failed: {e}")

print("\nTesting basic functionality...")

# Test NumPy
try:
    arr = np.array([1, 2, 3])
    print(f"✓ NumPy array created: {arr}")
except Exception as e:
    print(f"✗ NumPy test failed: {e}")

# Test Pandas
try:
    df = pd.DataFrame({'a': [1, 2], 'b': [3, 4]})
    print(f"✓ Pandas DataFrame created: {df.shape}")
except Exception as e:
    print(f"✗ Pandas test failed: {e}")

# Test SQLite
try:
    conn = sqlite3.connect(':memory:')
    cursor = conn.cursor()
    cursor.execute('CREATE TABLE test (id INTEGER)')
    conn.close()
    print("✓ SQLite test successful")
except Exception as e:
    print(f"✗ SQLite test failed: {e}")

print("\nAll tests completed!")
