{% extends "dashboard/base.html" %}

{% block title %}Prediksi Kelayakan Kredit{% endblock %}
{% block page_title %}Prediksi Kelayakan Kredit{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-10 mx-auto">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-calculator me-2"></i>
                    Form Prediksi Kelayakan Kredit
                </h4>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <!-- Data Pribadi -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-user me-2"></i>
                                Data Pribadi
                            </h5>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.nama.label(class="form-label") }}
                            {{ form.nama(class="form-control") }}
                            {% if form.nama.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.nama.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.umur.label(class="form-label") }}
                            {{ form.umur(class="form-control") }}
                            {% if form.umur.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.umur.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.status_pernikahan.label(class="form-label") }}
                            {{ form.status_pernikahan(class="form-select") }}
                            {% if form.status_pernikahan.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.status_pernikahan.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.jumlah_tanggungan.label(class="form-label") }}
                            {{ form.jumlah_tanggungan(class="form-control") }}
                            {% if form.jumlah_tanggungan.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.jumlah_tanggungan.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.pendidikan.label(class="form-label") }}
                            {{ form.pendidikan(class="form-select") }}
                            {% if form.pendidikan.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.pendidikan.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.kepemilikan_rumah.label(class="form-label") }}
                            {{ form.kepemilikan_rumah(class="form-select") }}
                            {% if form.kepemilikan_rumah.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.kepemilikan_rumah.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <hr>
                    
                    <!-- Data Pekerjaan -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-briefcase me-2"></i>
                                Data Pekerjaan
                            </h5>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.pekerjaan.label(class="form-label") }}
                            {{ form.pekerjaan(class="form-select") }}
                            {% if form.pekerjaan.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.pekerjaan.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.lama_bekerja.label(class="form-label") }}
                            {{ form.lama_bekerja(class="form-control") }}
                            {% if form.lama_bekerja.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.lama_bekerja.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-12 mb-3">
                            {{ form.pendapatan_bulanan.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">Rp</span>
                                {{ form.pendapatan_bulanan(class="form-control", id="pendapatan") }}
                            </div>
                            <div class="form-text">Masukkan pendapatan bulanan dalam Rupiah</div>
                            {% if form.pendapatan_bulanan.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.pendapatan_bulanan.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <hr>
                    
                    <!-- Data Kredit -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-credit-card me-2"></i>
                                Data Kredit
                            </h5>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.jumlah_kredit.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">Rp</span>
                                {{ form.jumlah_kredit(class="form-control", id="jumlahKredit") }}
                            </div>
                            {% if form.jumlah_kredit.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.jumlah_kredit.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.jangka_waktu.label(class="form-label") }}
                            {{ form.jangka_waktu(class="form-select") }}
                            {% if form.jangka_waktu.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.jangka_waktu.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-12 mb-3">
                            {{ form.riwayat_kredit.label(class="form-label") }}
                            {{ form.riwayat_kredit(class="form-select") }}
                            {% if form.riwayat_kredit.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.riwayat_kredit.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Debt to Income Ratio Indicator -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-chart-pie me-2"></i>
                                        Analisis Debt-to-Income Ratio
                                    </h6>
                                    <div id="debtRatioIndicator">
                                        <p class="text-muted">Masukkan pendapatan dan jumlah kredit untuk melihat analisis</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="{{ url_for('dashboard') }}" class="btn btn-secondary me-md-2">
                                    <i class="fas fa-arrow-left me-1"></i>
                                    Kembali
                                </a>
                                {{ form.submit(class="btn btn-primary btn-lg") }}
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Info Panel -->
<div class="row mt-4">
    <div class="col-md-10 mx-auto">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Informasi Penting
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>📋 Checklist Data</h6>
                        <ul class="small">
                            <li>Pastikan semua data akurat dan terkini</li>
                            <li>Verifikasi dokumen pendukung tersedia</li>
                            <li>Periksa kelengkapan informasi keuangan</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>🎯 Tips Prediksi</h6>
                        <ul class="small">
                            <li>Rasio kredit terhadap pendapatan ideal < 30%</li>
                            <li>Riwayat kredit yang baik meningkatkan peluang</li>
                            <li>Stabilitas pekerjaan menjadi faktor penting</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Format currency input
function formatCurrency(input) {
    let value = input.value.replace(/\D/g, '');
    input.value = value;
}

document.getElementById('pendapatan').addEventListener('input', function() {
    formatCurrency(this);
    calculateDebtRatio();
});

document.getElementById('jumlahKredit').addEventListener('input', function() {
    formatCurrency(this);
    calculateDebtRatio();
});

document.getElementById('jangka_waktu').addEventListener('change', calculateDebtRatio);

function calculateDebtRatio() {
    const pendapatan = parseFloat(document.getElementById('pendapatan').value) || 0;
    const kredit = parseFloat(document.getElementById('jumlahKredit').value) || 0;
    const jangkaWaktu = parseInt(document.getElementById('jangka_waktu').value) || 12;
    
    if (pendapatan > 0 && kredit > 0) {
        const monthlyPayment = kredit / jangkaWaktu;
        const ratio = (monthlyPayment / pendapatan) * 100;
        
        let status, color, icon;
        if (ratio < 30) {
            status = 'Sangat Baik';
            color = 'success';
            icon = 'fa-check-circle';
        } else if (ratio < 50) {
            status = 'Baik';
            color = 'warning';
            icon = 'fa-exclamation-triangle';
        } else {
            status = 'Berisiko Tinggi';
            color = 'danger';
            icon = 'fa-times-circle';
        }
        
        document.getElementById('debtRatioIndicator').innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas ${icon} text-${color} me-2"></i>
                <div>
                    <strong>Debt-to-Income Ratio: ${ratio.toFixed(1)}%</strong>
                    <br>
                    <small class="text-${color}">Status: ${status}</small>
                </div>
            </div>
            <div class="progress mt-2" style="height: 10px;">
                <div class="progress-bar bg-${color}" style="width: ${Math.min(ratio, 100)}%"></div>
            </div>
        `;
    }
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const pendapatan = parseFloat(document.getElementById('pendapatan').value) || 0;
    const kredit = parseFloat(document.getElementById('jumlahKredit').value) || 0;
    
    if (kredit > pendapatan * 60) { // 5 tahun
        if (!confirm('Jumlah kredit sangat besar dibanding pendapatan. Yakin ingin melanjutkan?')) {
            e.preventDefault();
        }
    }
});
</script>
{% endblock %}
