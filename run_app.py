#!/usr/bin/env python3
"""
Simple runner untuk aplikasi prediksi kredit
"""

import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("Starting Credit Prediction System...")
print("Python version:", sys.version)
print("Current directory:", os.getcwd())

try:
    print("Importing Flask...")
    from flask import Flask
    
    print("Importing models...")
    from models.neural_network import NeuralNetwork
    from models.database import CreditDatabase
    
    print("Importing config...")
    from config import Config
    
    print("All imports successful!")
    
    # Initialize Flask app
    app = Flask(__name__)
    app.config.from_object(Config)
    
    print("Flask app initialized")
    
    # Initialize database
    print("Initializing database...")
    db = CreditDatabase(app.config['DATABASE_PATH'])
    print(f"Database initialized with {db.count_records()} records")
    
    # Initialize model
    print("Initializing neural network...")
    X, y = db.get_training_data()
    model = NeuralNetwork(
        input_size=X.shape[1],
        hidden_layers=app.config['HIDDEN_LAYERS'],
        learning_rate=app.config['LEARNING_RATE']
    )
    print("Neural network initialized")
    
    # Train model if needed
    if not os.path.exists(app.config['MODEL_PATH']):
        print("Training new model...")
        losses = model.train(X, y, epochs=100, verbose=True)  # Reduced epochs for faster startup
        model.save_model(app.config['MODEL_PATH'])
        accuracy = model.calculate_accuracy(X, y)
        print(f"Model trained with accuracy: {accuracy:.4f}")
    else:
        print("Loading existing model...")
        model.load_model(app.config['MODEL_PATH'])
        accuracy = model.calculate_accuracy(X, y)
        print(f"Model loaded with accuracy: {accuracy:.4f}")
    
    print("\n" + "="*50)
    print("SISTEM PREDIKSI KELAYAKAN KREDIT")
    print("Neural Network dengan Backpropagation")
    print("="*50)
    print(f"Database: {db.count_records()} records")
    print(f"Model accuracy: {accuracy:.2%}")
    print("Server starting on http://localhost:5000")
    print("="*50)
    
    # Import routes after everything is initialized
    from app import app as main_app
    
    # Run the app
    main_app.run(debug=True, host='0.0.0.0', port=5000)
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
    input("Press Enter to exit...")
