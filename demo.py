#!/usr/bin/env python3
"""
Demo sistem prediksi kelayakan kredit
"""

import numpy as np
import os
import sys

def demo_neural_network():
    print("🧠 DEMO NEURAL NETWORK")
    print("-" * 40)
    
    try:
        from models.neural_network import NeuralNetwork
        
        # Create neural network
        print("1. Membuat Neural Network...")
        nn = NeuralNetwork(input_size=11, hidden_layers=[10, 8], learning_rate=0.01)
        print("   ✅ Neural Network berhasil dibuat")
        print(f"   📊 Arsitektur: 11 -> [10, 8] -> 1")
        
        # Generate sample data
        print("\n2. Membuat data training sample...")
        np.random.seed(42)
        n_samples = 100
        
        # Generate realistic credit data
        X = np.random.rand(n_samples, 11)
        # Normalize to realistic ranges
        X[:, 0] = X[:, 0] * 40 + 25  # umur: 25-65
        X[:, 1] = X[:, 1] * 10000000 + 2000000  # pendapatan: 2-12 juta
        X[:, 2] = np.random.randint(0, 6, n_samples)  # tanggungan: 0-5
        X[:, 3] = X[:, 3] * 20 + 1  # lama bekerja: 1-21 tahun
        X[:, 4] = X[:, 4] * 50000000 + 5000000  # jumlah kredit: 5-55 juta
        X[:, 5] = np.random.choice([12, 24, 36, 48, 60], n_samples)  # jangka waktu
        X[:, 6] = np.random.choice([0, 1], n_samples, p=[0.3, 0.7])  # riwayat kredit
        X[:, 7] = np.random.choice([0, 1], n_samples, p=[0.4, 0.6])  # status nikah
        X[:, 8] = np.random.choice([0, 1, 2, 3, 4], n_samples)  # pendidikan
        X[:, 9] = np.random.choice([0, 1], n_samples, p=[0.3, 0.7])  # pekerjaan
        X[:, 10] = np.random.choice([0, 1], n_samples, p=[0.6, 0.4])  # rumah
        
        # Normalize features
        X_norm = X.copy()
        for i in range(X.shape[1]):
            min_val, max_val = X[:, i].min(), X[:, i].max()
            if max_val > min_val:
                X_norm[:, i] = (X[:, i] - min_val) / (max_val - min_val)
        
        # Generate target based on simple rules
        y = np.zeros((n_samples, 1))
        for i in range(n_samples):
            score = 0
            # Good income vs credit ratio
            if X[i, 4] / (X[i, 1] * 12) < 3:  # debt to annual income < 3
                score += 2
            # Good credit history
            if X[i, 6] == 1:
                score += 2
            # Stable job
            if X[i, 9] == 1:
                score += 1
            # Long work experience
            if X[i, 3] > 5:
                score += 1
            # Own house
            if X[i, 10] == 1:
                score += 1
            
            y[i, 0] = 1 if score >= 4 else 0
        
        print(f"   ✅ Data training dibuat: {n_samples} samples")
        print(f"   📈 Distribusi: {np.sum(y)} layak, {n_samples - np.sum(y)} tidak layak")
        
        # Train model
        print("\n3. Training Neural Network...")
        losses = nn.train(X_norm, y, epochs=200, verbose=False)
        accuracy = nn.calculate_accuracy(X_norm, y)
        
        print(f"   ✅ Training selesai")
        print(f"   📊 Akurasi: {accuracy:.2%}")
        print(f"   📉 Final loss: {losses[-1]:.4f}")
        
        # Test prediction
        print("\n4. Test Prediksi...")
        test_case = X_norm[0:1]  # Ambil sample pertama
        prediction = nn.predict(test_case)[0][0]
        actual = y[0][0]
        
        print(f"   🎯 Prediksi: {prediction:.3f} ({'LAYAK' if prediction >= 0.5 else 'TIDAK LAYAK'})")
        print(f"   ✅ Aktual: {'LAYAK' if actual == 1 else 'TIDAK LAYAK'}")
        print(f"   🎯 Status: {'BENAR' if (prediction >= 0.5) == (actual == 1) else 'SALAH'}")
        
        # Save model
        print("\n5. Menyimpan Model...")
        os.makedirs('data', exist_ok=True)
        nn.save_model('data/demo_model.pkl')
        print("   ✅ Model disimpan ke data/demo_model.pkl")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def demo_database():
    print("\n💾 DEMO DATABASE")
    print("-" * 40)
    
    try:
        from models.database import CreditDatabase
        
        # Create database
        print("1. Membuat Database...")
        db = CreditDatabase('data/demo_credit.db')
        print("   ✅ Database berhasil dibuat")
        
        # Get statistics
        stats = db.get_statistics()
        print(f"   📊 Total aplikasi: {stats['total_applications']}")
        print(f"   ✅ Disetujui: {stats['approved']}")
        print(f"   ❌ Ditolak: {stats['rejected']}")
        
        # Get training data
        print("\n2. Mengambil Data Training...")
        X, y = db.get_training_data()
        print(f"   ✅ Data shape: {X.shape}")
        print(f"   📊 Features: {X.shape[1]}")
        print(f"   📈 Samples: {X.shape[0]}")
        
        # Add new application
        print("\n3. Menambah Aplikasi Baru...")
        new_app = {
            'nama': 'John Doe Demo',
            'umur': 35,
            'pendapatan_bulanan': 8000000,
            'jumlah_tanggungan': 2,
            'lama_bekerja': 8,
            'jumlah_kredit': 50000000,
            'jangka_waktu': 36,
            'riwayat_kredit': 1,
            'status_pernikahan': 1,
            'pendidikan': 3,
            'pekerjaan': 1,
            'kepemilikan_rumah': 1,
            'kelayakan': -1  # Belum diketahui
        }
        
        app_id = db.add_application(new_app)
        print(f"   ✅ Aplikasi ditambahkan dengan ID: {app_id}")
        
        # Save prediction
        db.save_prediction(app_id, 0.85, 1)
        print("   ✅ Prediksi disimpan")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def demo_complete_prediction():
    print("\n🎯 DEMO PREDIKSI LENGKAP")
    print("-" * 40)
    
    try:
        from models.database import CreditDatabase
        from models.neural_network import NeuralNetwork
        
        # Setup
        db = CreditDatabase('data/demo_credit.db')
        X, y = db.get_training_data()
        
        # Create and train model
        print("1. Training Model untuk Prediksi...")
        model = NeuralNetwork(input_size=X.shape[1], hidden_layers=[10, 8])
        losses = model.train(X, y, epochs=100, verbose=False)
        accuracy = model.calculate_accuracy(X, y)
        print(f"   ✅ Model trained dengan akurasi: {accuracy:.2%}")
        
        # Test cases
        test_cases = [
            {
                'nama': 'Nasabah A - Profil Baik',
                'data': [30, 10000000, 1, 8, 30000000, 36, 1, 1, 3, 1, 1]
            },
            {
                'nama': 'Nasabah B - Profil Sedang',
                'data': [45, 6000000, 3, 5, 40000000, 48, 1, 0, 2, 1, 0]
            },
            {
                'nama': 'Nasabah C - Profil Berisiko',
                'data': [25, 3000000, 4, 1, 50000000, 60, 0, 0, 1, 0, 0]
            }
        ]
        
        print("\n2. Testing Prediksi...")
        for i, case in enumerate(test_cases, 1):
            # Normalize input
            input_data = np.array([case['data']])
            for j in range(input_data.shape[1]):
                min_val, max_val = X[:, j].min(), X[:, j].max()
                if max_val > min_val:
                    input_data[0, j] = (input_data[0, j] - min_val) / (max_val - min_val)
            
            # Predict
            prob = model.predict(input_data)[0][0]
            decision = 'LAYAK' if prob >= 0.5 else 'TIDAK LAYAK'
            confidence = prob if prob >= 0.5 else (1 - prob)
            
            print(f"\n   Test Case {i}: {case['nama']}")
            print(f"   📊 Probabilitas: {prob:.3f}")
            print(f"   🎯 Keputusan: {decision}")
            print(f"   💪 Confidence: {confidence:.1%}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🚀 DEMO SISTEM PREDIKSI KELAYAKAN KREDIT")
    print("   Neural Network dengan Backpropagation")
    print("=" * 60)
    
    # Demo 1: Neural Network
    if not demo_neural_network():
        print("❌ Demo Neural Network gagal!")
        return
    
    # Demo 2: Database
    if not demo_database():
        print("❌ Demo Database gagal!")
        return
    
    # Demo 3: Complete Prediction
    if not demo_complete_prediction():
        print("❌ Demo Prediksi gagal!")
        return
    
    print("\n" + "=" * 60)
    print("🎉 SEMUA DEMO BERHASIL!")
    print("✅ Neural Network: OK")
    print("✅ Database: OK") 
    print("✅ Prediksi: OK")
    print("✅ Model Training: OK")
    print("\n🌐 Sistem siap untuk dijalankan sebagai web application!")
    print("   Jalankan: python app.py")
    print("=" * 60)

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Demo dihentikan")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
