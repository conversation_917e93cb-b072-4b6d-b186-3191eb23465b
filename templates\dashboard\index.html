{% extends "dashboard/base.html" %}

{% block title %}Dashboard - Sistem Prediksi Kelayakan Kredit{% endblock %}
{% block page_title %}Dashboard{% endblock %}

{% block content %}
<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2">
                            Selamat datang, {{ current_user.full_name }}! 👋
                        </h3>
                        <p class="text-muted mb-0">
                            Sistem Prediksi Kelayakan Kredit dengan Neural Network Backpropagation
                        </p>
                        <small class="text-muted">
                            Role: <span class="badge bg-primary">{{ current_user.role.title() }}</span>
                        </small>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="d-flex gap-2 justify-content-end">
                            <a href="{{ url_for('predict') }}" class="btn btn-primary">
                                <i class="fas fa-calculator me-1"></i>
                                Prediksi Baru
                            </a>
                            {% if current_user.is_manager() %}
                            <a href="{{ url_for('train') }}" class="btn btn-success">
                                <i class="fas fa-brain me-1"></i>
                                Training
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <!-- Credit Statistics -->
    <div class="col-md-3 mb-3">
        <div class="stats-card text-center">
            <div class="mb-3">
                <i class="fas fa-database fa-3x text-primary"></i>
            </div>
            <h4 class="text-primary">{{ credit_stats.total_applications }}</h4>
            <p class="text-muted mb-0">Total Aplikasi</p>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="stats-card text-center">
            <div class="mb-3">
                <i class="fas fa-check-circle fa-3x text-success"></i>
            </div>
            <h4 class="text-success">{{ credit_stats.approved }}</h4>
            <p class="text-muted mb-0">Disetujui</p>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="stats-card text-center">
            <div class="mb-3">
                <i class="fas fa-times-circle fa-3x text-danger"></i>
            </div>
            <h4 class="text-danger">{{ credit_stats.rejected }}</h4>
            <p class="text-muted mb-0">Ditolak</p>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="stats-card text-center">
            <div class="mb-3">
                <i class="fas fa-brain fa-3x text-warning"></i>
            </div>
            <h4 class="text-warning">{{ credit_stats.total_predictions }}</h4>
            <p class="text-muted mb-0">Prediksi</p>
        </div>
    </div>
</div>

<!-- User Statistics (Admin/Manager only) -->
{% if current_user.is_manager() %}
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="stats-card text-center">
            <div class="mb-3">
                <i class="fas fa-users fa-3x text-info"></i>
            </div>
            <h4 class="text-info">{{ user_stats.total_users }}</h4>
            <p class="text-muted mb-0">Total Users</p>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="stats-card text-center">
            <div class="mb-3">
                <i class="fas fa-user-clock fa-3x text-secondary"></i>
            </div>
            <h4 class="text-secondary">{{ user_stats.recent_logins }}</h4>
            <p class="text-muted mb-0">Login 24 Jam</p>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="stats-card text-center">
            <div class="mb-3">
                <i class="fas fa-chart-line fa-3x text-primary"></i>
            </div>
            <h4 class="text-primary">{{ user_stats.activities_today }}</h4>
            <p class="text-muted mb-0">Aktivitas Hari Ini</p>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="stats-card text-center">
            <div class="mb-3">
                <i class="fas fa-percentage fa-3x text-success"></i>
            </div>
            <h4 class="text-success">
                {% if credit_stats.total_applications > 0 %}
                    {{ "%.1f"|format((credit_stats.approved / credit_stats.total_applications * 100)) }}%
                {% else %}
                    0%
                {% endif %}
            </h4>
            <p class="text-muted mb-0">Approval Rate</p>
        </div>
    </div>
</div>
{% endif %}

<!-- Quick Actions & Recent Activity -->
<div class="row">
    <!-- Quick Actions -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('predict') }}" class="btn btn-outline-primary">
                        <i class="fas fa-calculator me-2"></i>
                        Buat Prediksi Baru
                    </a>
                    
                    {% if current_user.is_manager() %}
                    <a href="{{ url_for('train') }}" class="btn btn-outline-success">
                        <i class="fas fa-brain me-2"></i>
                        Training Model Neural Network
                    </a>
                    {% endif %}
                    
                    {% if current_user.is_admin() %}
                    <a href="{{ url_for('admin_users') }}" class="btn btn-outline-info">
                        <i class="fas fa-users me-2"></i>
                        Kelola User
                    </a>
                    
                    <a href="{{ url_for('admin_analytics') }}" class="btn btn-outline-warning">
                        <i class="fas fa-chart-bar me-2"></i>
                        Lihat Analytics
                    </a>
                    {% endif %}
                    
                    <a href="{{ url_for('profile') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-user me-2"></i>
                        Edit Profil
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- System Information -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Informasi Sistem
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <h6 class="text-muted">Neural Network</h6>
                        <ul class="list-unstyled small">
                            <li><i class="fas fa-check text-success me-1"></i> Backpropagation</li>
                            <li><i class="fas fa-check text-success me-1"></i> 11 Input Features</li>
                            <li><i class="fas fa-check text-success me-1"></i> 2 Hidden Layers</li>
                            <li><i class="fas fa-check text-success me-1"></i> Sigmoid Activation</li>
                        </ul>
                    </div>
                    <div class="col-6">
                        <h6 class="text-muted">Database</h6>
                        <ul class="list-unstyled small">
                            <li><i class="fas fa-check text-success me-1"></i> SQLite</li>
                            <li><i class="fas fa-check text-success me-1"></i> Auto Backup</li>
                            <li><i class="fas fa-check text-success me-1"></i> Real-time Sync</li>
                            <li><i class="fas fa-check text-success me-1"></i> Data Encryption</li>
                        </ul>
                    </div>
                </div>
                
                <div class="mt-3 p-2 bg-light rounded">
                    <small class="text-muted">
                        <i class="fas fa-lightbulb me-1"></i>
                        <strong>Tips:</strong> Lakukan training ulang model secara berkala untuk meningkatkan akurasi prediksi.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Predictions (if any) -->
{% if recent_predictions %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    Prediksi Terbaru
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Nama</th>
                                <th>Probabilitas</th>
                                <th>Status</th>
                                <th>Tanggal</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for prediction in recent_predictions %}
                            <tr>
                                <td>{{ prediction.nama }}</td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-{{ 'success' if prediction.probability >= 0.5 else 'danger' }}" 
                                             style="width: {{ prediction.probability * 100 }}%">
                                            {{ "%.1f"|format(prediction.probability * 100) }}%
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if prediction.status == 'LAYAK' else 'danger' }}">
                                        {{ prediction.status }}
                                    </span>
                                </td>
                                <td>{{ prediction.created_at }}</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
