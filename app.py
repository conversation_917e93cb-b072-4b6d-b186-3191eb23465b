from flask import Flask, render_template, request, jsonify, redirect, url_for
import numpy as np
import os
import json
from models.neural_network import NeuralNetwork
from models.database import CreditDatabase
from config import Config

app = Flask(__name__)
app.config.from_object(Config)

# Initialize database
db = CreditDatabase(app.config['DATABASE_PATH'])

# Global variable untuk model
model = None

def load_or_create_model():
    """Load model yang sudah ada atau buat model baru"""
    global model

    # Get training data
    X, y = db.get_training_data()
    input_size = X.shape[1]

    # Create model
    model = NeuralNetwork(
        input_size=input_size,
        hidden_layers=app.config['HIDDEN_LAYERS'],
        learning_rate=app.config['LEARNING_RATE']
    )

    # Load model jika ada
    if os.path.exists(app.config['MODEL_PATH']):
        try:
            model.load_model(app.config['MODEL_PATH'])
            print("Model loaded successfully")
        except Exception as e:
            print(f"Error loading model: {e}")
            print("Training new model...")
            train_model()
    else:
        print("No existing model found. Training new model...")
        train_model()

def train_model():
    """Train model dengan data yang ada"""
    global model

    print("Starting model training...")
    X, y = db.get_training_data()

    # Train model
    losses = model.train(X, y, epochs=app.config['EPOCHS'], verbose=True)

    # Save model
    model.save_model(app.config['MODEL_PATH'])

    # Calculate final accuracy
    accuracy = model.calculate_accuracy(X, y)
    print(f"Training completed. Final accuracy: {accuracy:.4f}")

    return losses, accuracy

def normalize_input(data):
    """Normalize input data untuk prediksi"""
    # Ambil sample data untuk mendapatkan min/max values
    X, _ = db.get_training_data()

    # Convert input ke array
    input_array = np.array([[
        data['umur'],
        data['pendapatan_bulanan'],
        data['jumlah_tanggungan'],
        data['lama_bekerja'],
        data['jumlah_kredit'],
        data['jangka_waktu'],
        data['riwayat_kredit'],
        data['status_pernikahan'],
        data['pendidikan'],
        data['pekerjaan'],
        data['kepemilikan_rumah']
    ]])

    # Normalize menggunakan min-max dari training data
    normalized = input_array.copy().astype(float)
    for i in range(X.shape[1]):
        min_val = X[:, i].min()
        max_val = X[:, i].max()
        if max_val > min_val:
            normalized[0, i] = (input_array[0, i] - min_val) / (max_val - min_val)

    return normalized

@app.route('/')
def index():
    """Homepage"""
    stats = db.get_statistics()
    return render_template('index.html', stats=stats)

@app.route('/predict', methods=['GET', 'POST'])
def predict():
    """Halaman prediksi"""
    if request.method == 'POST':
        try:
            # Get form data
            data = {
                'nama': request.form['nama'],
                'umur': int(request.form['umur']),
                'pendapatan_bulanan': float(request.form['pendapatan_bulanan']),
                'jumlah_tanggungan': int(request.form['jumlah_tanggungan']),
                'lama_bekerja': int(request.form['lama_bekerja']),
                'jumlah_kredit': float(request.form['jumlah_kredit']),
                'jangka_waktu': int(request.form['jangka_waktu']),
                'riwayat_kredit': int(request.form['riwayat_kredit']),
                'status_pernikahan': int(request.form['status_pernikahan']),
                'pendidikan': int(request.form['pendidikan']),
                'pekerjaan': int(request.form['pekerjaan']),
                'kepemilikan_rumah': int(request.form['kepemilikan_rumah'])
            }

            # Normalize input
            normalized_input = normalize_input(data)

            # Make prediction
            probability = model.predict(normalized_input)[0][0]
            predicted_class = 1 if probability >= 0.5 else 0

            # Save to database
            app_id = db.add_application(data)
            db.save_prediction(app_id, float(probability), predicted_class)

            # Prepare result
            result = {
                'nama': data['nama'],
                'probability': float(probability),
                'predicted_class': predicted_class,
                'status': 'LAYAK' if predicted_class == 1 else 'TIDAK LAYAK',
                'confidence': f"{probability * 100:.1f}%" if predicted_class == 1 else f"{(1-probability) * 100:.1f}%"
            }

            return render_template('result.html', result=result)

        except Exception as e:
            return render_template('predict.html', error=str(e))

    return render_template('predict.html')

@app.route('/train', methods=['GET', 'POST'])
def train():
    """Halaman training model"""
    if request.method == 'POST':
        try:
            losses, accuracy = train_model()
            return render_template('train.html',
                                 success=True,
                                 accuracy=accuracy,
                                 message="Model berhasil dilatih!")
        except Exception as e:
            return render_template('train.html',
                                 error=str(e))

    return render_template('train.html')

@app.route('/api/predict', methods=['POST'])
def api_predict():
    """API endpoint untuk prediksi"""
    try:
        data = request.json

        # Normalize input
        normalized_input = normalize_input(data)

        # Make prediction
        probability = model.predict(normalized_input)[0][0]
        predicted_class = 1 if probability >= 0.5 else 0

        return jsonify({
            'success': True,
            'probability': float(probability),
            'predicted_class': predicted_class,
            'status': 'LAYAK' if predicted_class == 1 else 'TIDAK LAYAK'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 400

@app.route('/api/train', methods=['POST'])
def api_train():
    """API endpoint untuk training"""
    try:
        losses, accuracy = train_model()
        return jsonify({
            'success': True,
            'accuracy': accuracy,
            'message': 'Model berhasil dilatih'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 400

@app.route('/api/stats')
def api_stats():
    """API endpoint untuk statistik"""
    stats = db.get_statistics()
    return jsonify(stats)

def initialize_system():
    """Initialize the complete system"""
    global model, db
    load_or_create_model()
    return True

if __name__ == '__main__':
    # Initialize model saat startup
    initialize_system()

    # Run app
    app.run(debug=True, host='0.0.0.0', port=5000)
