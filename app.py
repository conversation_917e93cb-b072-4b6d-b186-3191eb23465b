from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, session
from flask_login import <PERSON>gin<PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
import numpy as np
import os
import json
from datetime import datetime
from models.neural_network import NeuralNetwork
from models.database import CreditDatabase
from models.user import User<PERSON>anager, User
from forms import LoginForm, RegisterForm, PredictionForm, ChangePasswordForm, UserManagementForm
from config import Config

app = Flask(__name__)
app.config.from_object(Config)

# Initialize Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Silakan login untuk mengakses halaman ini.'
login_manager.login_message_category = 'info'

# Initialize databases
db = CreditDatabase(app.config['DATABASE_PATH'])
user_manager = UserManager(app.config['USER_DATABASE_PATH'])

# Global variable untuk model
model = None

@login_manager.user_loader
def load_user(user_id):
    return user_manager.get_user_by_id(int(user_id))

def load_or_create_model():
    """Load model yang sudah ada atau buat model baru"""
    global model

    # Get training data
    X, y = db.get_training_data()
    input_size = X.shape[1]

    # Create model
    model = NeuralNetwork(
        input_size=input_size,
        hidden_layers=app.config['HIDDEN_LAYERS'],
        learning_rate=app.config['LEARNING_RATE']
    )

    # Load model jika ada
    if os.path.exists(app.config['MODEL_PATH']):
        try:
            model.load_model(app.config['MODEL_PATH'])
            print("Model loaded successfully")
        except Exception as e:
            print(f"Error loading model: {e}")
            print("Training new model...")
            train_model()
    else:
        print("No existing model found. Training new model...")
        train_model()

def train_model():
    """Train model dengan data yang ada"""
    global model

    print("Starting model training...")
    X, y = db.get_training_data()

    # Train model
    losses = model.train(X, y, epochs=app.config['EPOCHS'], verbose=True)

    # Save model
    model.save_model(app.config['MODEL_PATH'])

    # Calculate final accuracy
    accuracy = model.calculate_accuracy(X, y)
    print(f"Training completed. Final accuracy: {accuracy:.4f}")

    return losses, accuracy

def normalize_input(data):
    """Normalize input data untuk prediksi"""
    # Ambil sample data untuk mendapatkan min/max values
    X, _ = db.get_training_data()

    # Convert input ke array
    input_array = np.array([[
        data['umur'],
        data['pendapatan_bulanan'],
        data['jumlah_tanggungan'],
        data['lama_bekerja'],
        data['jumlah_kredit'],
        data['jangka_waktu'],
        data['riwayat_kredit'],
        data['status_pernikahan'],
        data['pendidikan'],
        data['pekerjaan'],
        data['kepemilikan_rumah']
    ]])

    # Normalize menggunakan min-max dari training data
    normalized = input_array.copy().astype(float)
    for i in range(X.shape[1]):
        min_val = X[:, i].min()
        max_val = X[:, i].max()
        if max_val > min_val:
            normalized[0, i] = (input_array[0, i] - min_val) / (max_val - min_val)

    return normalized

# Authentication Routes
@app.route('/login', methods=['GET', 'POST'])
def login():
    """Login page"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    form = LoginForm()
    if form.validate_on_submit():
        user = user_manager.authenticate_user(
            username=form.username.data,
            password=form.password.data,
            ip_address=request.remote_addr
        )

        if user:
            login_user(user, remember=form.remember_me.data)
            flash(f'Selamat datang, {user.full_name}!', 'success')

            # Redirect ke halaman yang diminta atau dashboard
            next_page = request.args.get('next')
            if not next_page or not next_page.startswith('/'):
                next_page = url_for('dashboard')
            return redirect(next_page)
        else:
            flash('Username atau password salah!', 'danger')

    return render_template('auth/login.html', form=form)

@app.route('/register', methods=['GET', 'POST'])
def register():
    """Register page - hanya untuk admin"""
    if current_user.is_authenticated and not current_user.is_admin():
        flash('Anda tidak memiliki akses untuk mendaftar user baru.', 'warning')
        return redirect(url_for('dashboard'))

    form = RegisterForm()
    if form.validate_on_submit():
        user_id = user_manager.create_user(
            username=form.username.data,
            email=form.email.data,
            password=form.password.data,
            full_name=form.full_name.data,
            role=form.role.data
        )

        if user_id:
            flash(f'User {form.username.data} berhasil didaftarkan!', 'success')
            if current_user.is_authenticated:
                return redirect(url_for('admin_users'))
            else:
                return redirect(url_for('login'))
        else:
            flash('Gagal mendaftarkan user. Username atau email sudah digunakan.', 'danger')

    return render_template('auth/register.html', form=form)

@app.route('/logout')
@login_required
def logout():
    """Logout user"""
    user_manager.log_activity(
        user_id=current_user.id,
        action='LOGOUT',
        description=f'User {current_user.username} logged out',
        ip_address=request.remote_addr
    )
    logout_user()
    flash('Anda telah berhasil logout.', 'info')
    return redirect(url_for('login'))

# Dashboard Routes
@app.route('/')
@app.route('/dashboard')
@login_required
def dashboard():
    """Main dashboard"""
    # Get statistics
    credit_stats = db.get_statistics()
    user_stats = user_manager.get_user_statistics()

    # Get recent activities untuk current user
    recent_predictions = get_recent_predictions(current_user.id, limit=5)

    return render_template('dashboard/index.html',
                         credit_stats=credit_stats,
                         user_stats=user_stats,
                         recent_predictions=recent_predictions)

def get_recent_predictions(user_id, limit=10):
    """Get recent predictions for user"""
    # Implementasi sederhana - bisa diperluas
    return []

@app.route('/predict', methods=['GET', 'POST'])
@login_required
def predict():
    """Halaman prediksi"""
    form = PredictionForm()

    if form.validate_on_submit():
        try:
            # Get form data
            data = {
                'nama': form.nama.data,
                'umur': form.umur.data,
                'pendapatan_bulanan': form.pendapatan_bulanan.data,
                'jumlah_tanggungan': form.jumlah_tanggungan.data,
                'lama_bekerja': form.lama_bekerja.data,
                'jumlah_kredit': form.jumlah_kredit.data,
                'jangka_waktu': form.jangka_waktu.data,
                'riwayat_kredit': form.riwayat_kredit.data,
                'status_pernikahan': form.status_pernikahan.data,
                'pendidikan': form.pendidikan.data,
                'pekerjaan': form.pekerjaan.data,
                'kepemilikan_rumah': form.kepemilikan_rumah.data
            }

            # Normalize input
            normalized_input = normalize_input(data)

            # Make prediction
            probability = model.predict(normalized_input)[0][0]
            predicted_class = 1 if probability >= 0.5 else 0

            # Save to database
            app_id = db.add_application(data)
            db.save_prediction(app_id, float(probability), predicted_class)

            # Log activity
            user_manager.log_activity(
                user_id=current_user.id,
                action='PREDICTION_MADE',
                description=f'Prediksi untuk {data["nama"]} - {data["status"] if predicted_class == 1 else "TIDAK LAYAK"}',
                ip_address=request.remote_addr
            )

            # Prepare result
            result = {
                'nama': data['nama'],
                'probability': float(probability),
                'predicted_class': predicted_class,
                'status': 'LAYAK' if predicted_class == 1 else 'TIDAK LAYAK',
                'confidence': f"{probability * 100:.1f}%" if predicted_class == 1 else f"{(1-probability) * 100:.1f}%",
                'processed_by': current_user.full_name,
                'processed_at': datetime.now().strftime('%d/%m/%Y %H:%M:%S')
            }

            flash(f'Prediksi untuk {data["nama"]} berhasil diproses!', 'success')
            return render_template('dashboard/result.html', result=result)

        except Exception as e:
            flash(f'Error dalam prediksi: {str(e)}', 'danger')

    return render_template('dashboard/predict.html', form=form)

# Training Routes
@app.route('/train', methods=['GET', 'POST'])
@login_required
def train():
    """Halaman training model - hanya untuk manager/admin"""
    if not current_user.is_manager():
        flash('Anda tidak memiliki akses untuk training model.', 'warning')
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        try:
            losses, accuracy = train_model()

            # Log activity
            user_manager.log_activity(
                user_id=current_user.id,
                action='MODEL_TRAINED',
                description=f'Model trained by {current_user.full_name} - Accuracy: {accuracy:.2%}',
                ip_address=request.remote_addr
            )

            flash(f'Model berhasil dilatih dengan akurasi {accuracy:.2%}!', 'success')
            return render_template('dashboard/train.html',
                                 success=True,
                                 accuracy=accuracy,
                                 message="Model berhasil dilatih!")
        except Exception as e:
            flash(f'Error dalam training: {str(e)}', 'danger')
            return render_template('dashboard/train.html', error=str(e))

    return render_template('dashboard/train.html')

# Admin Routes
@app.route('/admin/users')
@login_required
def admin_users():
    """Admin - User management"""
    if not current_user.is_admin():
        flash('Anda tidak memiliki akses admin.', 'warning')
        return redirect(url_for('dashboard'))

    users = user_manager.get_all_users()
    return render_template('admin/users.html', users=users)

@app.route('/admin/analytics')
@login_required
def admin_analytics():
    """Admin - Analytics dashboard"""
    if not current_user.is_admin():
        flash('Anda tidak memiliki akses admin.', 'warning')
        return redirect(url_for('dashboard'))

    # Get comprehensive statistics
    credit_stats = db.get_statistics()
    user_stats = user_manager.get_user_statistics()

    return render_template('admin/analytics.html',
                         credit_stats=credit_stats,
                         user_stats=user_stats)

# Profile Routes
@app.route('/profile')
@login_required
def profile():
    """User profile page"""
    return render_template('dashboard/profile.html')

@app.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """Change password page"""
    form = ChangePasswordForm()

    if form.validate_on_submit():
        # Verify current password
        if user_manager.verify_password(form.current_password.data, current_user.password_hash):
            # Update password
            new_hash = user_manager.hash_password(form.new_password.data)
            # Update in database (implementasi diperlukan)
            flash('Password berhasil diubah!', 'success')
            return redirect(url_for('profile'))
        else:
            flash('Password saat ini salah!', 'danger')

    return render_template('dashboard/change_password.html', form=form)

@app.route('/api/predict', methods=['POST'])
def api_predict():
    """API endpoint untuk prediksi"""
    try:
        data = request.json

        # Normalize input
        normalized_input = normalize_input(data)

        # Make prediction
        probability = model.predict(normalized_input)[0][0]
        predicted_class = 1 if probability >= 0.5 else 0

        return jsonify({
            'success': True,
            'probability': float(probability),
            'predicted_class': predicted_class,
            'status': 'LAYAK' if predicted_class == 1 else 'TIDAK LAYAK'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 400

@app.route('/api/train', methods=['POST'])
def api_train():
    """API endpoint untuk training"""
    try:
        losses, accuracy = train_model()
        return jsonify({
            'success': True,
            'accuracy': accuracy,
            'message': 'Model berhasil dilatih'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 400

@app.route('/api/stats')
def api_stats():
    """API endpoint untuk statistik"""
    stats = db.get_statistics()
    return jsonify(stats)

def initialize_system():
    """Initialize the complete system"""
    global model, db
    load_or_create_model()
    return True

if __name__ == '__main__':
    # Initialize model saat startup
    initialize_system()

    # Run app
    app.run(debug=True, host='0.0.0.0', port=5000)
