<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Sistem Prediksi Kelayakan Kredit</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .login-body {
            padding: 2rem;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px;
            font-weight: 600;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        .demo-accounts {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="login-container">
                    <div class="login-header">
                        <h2 class="mb-0">
                            <i class="fas fa-brain"></i>
                            Sistem Prediksi Kredit
                        </h2>
                        <p class="mb-0 mt-2">Neural Network dengan Backpropagation</p>
                    </div>
                    
                    <div class="login-body">
                        <!-- Flash Messages -->
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}
                        
                        <form method="POST">
                            {{ form.hidden_tag() }}
                            
                            <div class="mb-3">
                                {{ form.username.label(class="form-label") }}
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-user"></i>
                                    </span>
                                    {{ form.username(class="form-control") }}
                                </div>
                                {% if form.username.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.username.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.password.label(class="form-label") }}
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    {{ form.password(class="form-control") }}
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                {% if form.password.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.password.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3 form-check">
                                {{ form.remember_me(class="form-check-input") }}
                                {{ form.remember_me.label(class="form-check-label") }}
                            </div>
                            
                            <div class="d-grid">
                                {{ form.submit(class="btn btn-primary btn-login") }}
                            </div>
                        </form>
                        
                        <!-- Demo Accounts -->
                        <div class="demo-accounts">
                            <h6 class="text-muted mb-2">
                                <i class="fas fa-info-circle"></i> Demo Accounts
                            </h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <small class="text-muted">
                                        <strong>Admin:</strong><br>
                                        admin / admin123
                                    </small>
                                </div>
                                <div class="col-md-4">
                                    <small class="text-muted">
                                        <strong>Manager:</strong><br>
                                        manager / manager123
                                    </small>
                                </div>
                                <div class="col-md-4">
                                    <small class="text-muted">
                                        <strong>User:</strong><br>
                                        user / user123
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Footer -->
                <div class="text-center mt-3">
                    <small class="text-white">
                        © 2024 Sistem Prediksi Kelayakan Kredit
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordField = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
        
        // Auto-fill demo accounts
        document.addEventListener('DOMContentLoaded', function() {
            const demoButtons = document.querySelectorAll('.demo-accounts small');
            demoButtons.forEach(function(button) {
                button.style.cursor = 'pointer';
                button.addEventListener('click', function() {
                    const text = this.textContent;
                    if (text.includes('admin')) {
                        document.getElementById('username').value = 'admin';
                        document.getElementById('password').value = 'admin123';
                    } else if (text.includes('manager')) {
                        document.getElementById('username').value = 'manager';
                        document.getElementById('password').value = 'manager123';
                    } else if (text.includes('User')) {
                        document.getElementById('username').value = 'user';
                        document.getElementById('password').value = 'user123';
                    }
                });
            });
        });
    </script>
</body>
</html>
