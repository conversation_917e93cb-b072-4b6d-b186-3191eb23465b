import sqlite3
import hashlib
import os
from datetime import datetime
from typing import Optional, Dict, List
from flask_login import UserMixin

class User(UserMixin):
    def __init__(self, id, username, email, full_name, role='user', is_active=True):
        self.id = id
        self.username = username
        self.email = email
        self.full_name = full_name
        self.role = role
        self.is_active = is_active
    
    def get_id(self):
        return str(self.id)
    
    def is_admin(self):
        return self.role == 'admin'
    
    def is_manager(self):
        return self.role in ['admin', 'manager']

class UserManager:
    def __init__(self, db_path: str):
        """
        Initialize user management system
        
        Args:
            db_path: Path ke database file untuk users
        """
        self.db_path = db_path
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        self.init_database()
    
    def init_database(self):
        """Initialize user database tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Table untuk users
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT DEFAULT 'user',
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                login_count INTEGER DEFAULT 0
            )
        ''')
        
        # Table untuk user sessions
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                session_token TEXT NOT NULL,
                ip_address TEXT,
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP,
                is_active BOOLEAN DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # Table untuk activity logs
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS activity_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action TEXT NOT NULL,
                description TEXT,
                ip_address TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # Create default admin user if no users exist
        if self.count_users() == 0:
            self.create_default_users()
    
    def hash_password(self, password: str) -> str:
        """Hash password menggunakan SHA-256 dengan salt"""
        salt = "prediksi_kredit_salt_2024"
        return hashlib.sha256((password + salt).encode()).hexdigest()
    
    def verify_password(self, password: str, password_hash: str) -> bool:
        """Verify password dengan hash"""
        return self.hash_password(password) == password_hash
    
    def create_default_users(self):
        """Create default users untuk sistem"""
        default_users = [
            {
                'username': 'admin',
                'email': '<EMAIL>',
                'password': 'admin123',
                'full_name': 'Administrator',
                'role': 'admin'
            },
            {
                'username': 'manager',
                'email': '<EMAIL>',
                'password': 'manager123',
                'full_name': 'Credit Manager',
                'role': 'manager'
            },
            {
                'username': 'user',
                'email': '<EMAIL>',
                'password': 'user123',
                'full_name': 'Credit Analyst',
                'role': 'user'
            }
        ]
        
        for user_data in default_users:
            self.create_user(
                username=user_data['username'],
                email=user_data['email'],
                password=user_data['password'],
                full_name=user_data['full_name'],
                role=user_data['role']
            )
        
        print("Default users created:")
        print("- admin/admin123 (Administrator)")
        print("- manager/manager123 (Credit Manager)")
        print("- user/user123 (Credit Analyst)")
    
    def create_user(self, username: str, email: str, password: str, 
                   full_name: str, role: str = 'user') -> Optional[int]:
        """
        Create new user
        
        Returns:
            user_id jika berhasil, None jika gagal
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            password_hash = self.hash_password(password)
            
            cursor.execute('''
                INSERT INTO users (username, email, password_hash, full_name, role)
                VALUES (?, ?, ?, ?, ?)
            ''', [username, email, password_hash, full_name, role])
            
            user_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            # Log activity
            self.log_activity(user_id, 'USER_CREATED', f'User {username} created')
            
            return user_id
            
        except sqlite3.IntegrityError:
            return None  # Username atau email sudah ada
        except Exception as e:
            print(f"Error creating user: {e}")
            return None
    
    def authenticate_user(self, username: str, password: str, 
                         ip_address: str = None) -> Optional[User]:
        """
        Authenticate user dengan username/password
        
        Returns:
            User object jika berhasil, None jika gagal
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, username, email, password_hash, full_name, role, is_active
            FROM users 
            WHERE username = ? AND is_active = 1
        ''', [username])
        
        result = cursor.fetchone()
        
        if result and self.verify_password(password, result[3]):
            user = User(
                id=result[0],
                username=result[1],
                email=result[2],
                full_name=result[4],
                role=result[5],
                is_active=bool(result[6])
            )
            
            # Update last login
            cursor.execute('''
                UPDATE users 
                SET last_login = CURRENT_TIMESTAMP, login_count = login_count + 1
                WHERE id = ?
            ''', [user.id])
            
            conn.commit()
            conn.close()
            
            # Log activity
            self.log_activity(user.id, 'LOGIN_SUCCESS', f'User {username} logged in', ip_address)
            
            return user
        else:
            conn.close()
            # Log failed login
            self.log_activity(None, 'LOGIN_FAILED', f'Failed login attempt for {username}', ip_address)
            return None
    
    def get_user_by_id(self, user_id: int) -> Optional[User]:
        """Get user by ID"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, username, email, full_name, role, is_active
            FROM users 
            WHERE id = ? AND is_active = 1
        ''', [user_id])
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return User(
                id=result[0],
                username=result[1],
                email=result[2],
                full_name=result[3],
                role=result[4],
                is_active=bool(result[5])
            )
        return None
    
    def count_users(self) -> int:
        """Count total users"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM users WHERE is_active = 1")
        count = cursor.fetchone()[0]
        conn.close()
        return count
    
    def get_all_users(self) -> List[Dict]:
        """Get all users untuk admin dashboard"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, username, email, full_name, role, is_active, 
                   created_at, last_login, login_count
            FROM users 
            ORDER BY created_at DESC
        ''')
        
        users = []
        for row in cursor.fetchall():
            users.append({
                'id': row[0],
                'username': row[1],
                'email': row[2],
                'full_name': row[3],
                'role': row[4],
                'is_active': bool(row[5]),
                'created_at': row[6],
                'last_login': row[7],
                'login_count': row[8]
            })
        
        conn.close()
        return users
    
    def log_activity(self, user_id: Optional[int], action: str, 
                    description: str = None, ip_address: str = None):
        """Log user activity"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO activity_logs (user_id, action, description, ip_address)
                VALUES (?, ?, ?, ?)
            ''', [user_id, action, description, ip_address])
            
            conn.commit()
            conn.close()
        except Exception as e:
            print(f"Error logging activity: {e}")
    
    def get_user_statistics(self) -> Dict:
        """Get user statistics untuk dashboard"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Total users
        cursor.execute("SELECT COUNT(*) FROM users WHERE is_active = 1")
        total_users = cursor.fetchone()[0]
        
        # Users by role
        cursor.execute('''
            SELECT role, COUNT(*) 
            FROM users 
            WHERE is_active = 1 
            GROUP BY role
        ''')
        users_by_role = dict(cursor.fetchall())
        
        # Recent logins (last 24 hours)
        cursor.execute('''
            SELECT COUNT(*) 
            FROM users 
            WHERE last_login >= datetime('now', '-1 day')
        ''')
        recent_logins = cursor.fetchone()[0]
        
        # Total activities today
        cursor.execute('''
            SELECT COUNT(*) 
            FROM activity_logs 
            WHERE date(created_at) = date('now')
        ''')
        activities_today = cursor.fetchone()[0]
        
        conn.close()
        
        return {
            'total_users': total_users,
            'users_by_role': users_by_role,
            'recent_logins': recent_logins,
            'activities_today': activities_today
        }
