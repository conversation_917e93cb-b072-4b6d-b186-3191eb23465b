#!/usr/bin/env python3
"""
Setup dan jalankan sistem prediksi kelayakan kredit
"""

import os
import sys
import time

def print_header():
    print("=" * 60)
    print("🧠 SISTEM PREDIKSI KELAYAKAN KREDIT")
    print("   Neural Network dengan Backpropagation")
    print("=" * 60)

def check_dependencies():
    print("📦 Checking dependencies...")
    
    required_modules = [
        ('numpy', 'NumPy'),
        ('pandas', 'Pandas'),
        ('flask', 'Flask'),
        ('sqlite3', 'SQLite3'),
        ('sklearn', 'Scikit-learn')
    ]
    
    missing = []
    for module, name in required_modules:
        try:
            __import__(module)
            print(f"  ✅ {name}")
        except ImportError:
            print(f"  ❌ {name} - MISSING")
            missing.append(name)
    
    if missing:
        print(f"\n⚠️  Missing dependencies: {', '.join(missing)}")
        print("Please install them using: pip install flask numpy pandas scikit-learn")
        return False
    
    print("✅ All dependencies available!")
    return True

def setup_directories():
    print("\n📁 Setting up directories...")
    
    directories = ['data', 'models', 'templates', 'static']
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"  ✅ Created {directory}/")
        else:
            print(f"  ✅ {directory}/ exists")

def test_neural_network():
    print("\n🧠 Testing Neural Network...")
    
    try:
        from models.neural_network import NeuralNetwork
        import numpy as np
        
        # Create test neural network
        nn = NeuralNetwork(input_size=3, hidden_layers=[5, 3])
        
        # Generate test data
        X = np.random.rand(20, 3)
        y = np.random.randint(0, 2, (20, 1))
        
        # Quick training test
        print("  🔄 Training test model...")
        losses = nn.train(X, y, epochs=50, verbose=False)
        accuracy = nn.calculate_accuracy(X, y)
        
        print(f"  ✅ Neural Network OK - Accuracy: {accuracy:.2%}")
        return True
        
    except Exception as e:
        print(f"  ❌ Neural Network Error: {e}")
        return False

def test_database():
    print("\n💾 Testing Database...")
    
    try:
        from models.database import CreditDatabase
        
        # Create test database
        test_db_path = 'data/test_credit.db'
        db = CreditDatabase(test_db_path)
        
        # Get statistics
        stats = db.get_statistics()
        print(f"  ✅ Database OK - {stats['total_applications']} records")
        
        # Test training data
        X, y = db.get_training_data()
        print(f"  ✅ Training data ready - Shape: {X.shape}")
        
        return True, db
        
    except Exception as e:
        print(f"  ❌ Database Error: {e}")
        return False, None

def train_model(db):
    print("\n🎯 Training Neural Network Model...")
    
    try:
        from models.neural_network import NeuralNetwork
        from config import Config
        
        # Get training data
        X, y = db.get_training_data()
        print(f"  📊 Training data: {X.shape[0]} samples, {X.shape[1]} features")
        
        # Create model
        model = NeuralNetwork(
            input_size=X.shape[1],
            hidden_layers=Config.HIDDEN_LAYERS,
            learning_rate=Config.LEARNING_RATE
        )
        
        print(f"  🏗️  Model architecture: {X.shape[1]} -> {Config.HIDDEN_LAYERS} -> 1")
        print(f"  ⚙️  Learning rate: {Config.LEARNING_RATE}")
        print(f"  🔄 Training for {Config.EPOCHS} epochs...")
        
        # Train model
        start_time = time.time()
        losses = model.train(X, y, epochs=Config.EPOCHS, verbose=True)
        training_time = time.time() - start_time
        
        # Calculate final accuracy
        accuracy = model.calculate_accuracy(X, y)
        
        # Save model
        model.save_model(Config.MODEL_PATH)
        
        print(f"  ✅ Training completed!")
        print(f"  📈 Final accuracy: {accuracy:.2%}")
        print(f"  ⏱️  Training time: {training_time:.1f} seconds")
        print(f"  💾 Model saved to: {Config.MODEL_PATH}")
        
        return True, model
        
    except Exception as e:
        print(f"  ❌ Training Error: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def start_web_server():
    print("\n🌐 Starting Web Server...")
    
    try:
        from flask import Flask
        from app import app
        
        print("  🚀 Flask application ready")
        print("  📍 Server URL: http://localhost:5000")
        print("  🔧 Debug mode: ON")
        print("\n" + "=" * 60)
        print("🎉 SISTEM SIAP DIGUNAKAN!")
        print("   Buka browser dan akses: http://localhost:5000")
        print("=" * 60)
        
        # Start server
        app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)
        
    except Exception as e:
        print(f"  ❌ Server Error: {e}")
        import traceback
        traceback.print_exc()

def main():
    print_header()
    
    # Step 1: Check dependencies
    if not check_dependencies():
        input("\nPress Enter to exit...")
        return
    
    # Step 2: Setup directories
    setup_directories()
    
    # Step 3: Test neural network
    if not test_neural_network():
        input("\nPress Enter to exit...")
        return
    
    # Step 4: Test database
    db_ok, db = test_database()
    if not db_ok:
        input("\nPress Enter to exit...")
        return
    
    # Step 5: Train model
    model_ok, model = train_model(db)
    if not model_ok:
        input("\nPress Enter to exit...")
        return
    
    # Step 6: Start web server
    start_web_server()

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Sistem dihentikan oleh user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        input("\nPress Enter to exit...")
