{% extends "base.html" %}

{% block title %}Training Model Neural Network{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-10 mx-auto">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h4 class="mb-0">
                    <i class="fas fa-brain"></i> Training Model Neural Network
                </h4>
            </div>
            <div class="card-body">
                {% if success %}
                <div class="alert alert-success" role="alert">
                    <h5><i class="fas fa-check-circle"></i> Training Berhasil!</h5>
                    <p class="mb-2">{{ message }}</p>
                    <p class="mb-0"><strong>Akurasi Model: {{ "%.2f"|format(accuracy * 100) }}%</strong></p>
                </div>
                {% endif %}

                {% if error %}
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle"></i> {{ error }}
                </div>
                {% endif %}

                <div class="row">
                    <div class="col-md-8">
                        <h5 class="text-success mb-3">
                            <i class="fas fa-cogs"></i> Proses Training Model
                        </h5>
                        
                        <p>
                            Sistem menggunakan algoritma <strong>Neural Network dengan Backpropagation</strong> 
                            untuk mempelajari pola dari data historis aplikasi kredit.
                        </p>

                        <div class="card mb-3">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-network-wired"></i> Arsitektur Neural Network
                                </h6>
                                <ul class="mb-0">
                                    <li><strong>Input Layer:</strong> 11 neuron (sesuai jumlah fitur)</li>
                                    <li><strong>Hidden Layer 1:</strong> 10 neuron</li>
                                    <li><strong>Hidden Layer 2:</strong> 8 neuron</li>
                                    <li><strong>Output Layer:</strong> 1 neuron (probabilitas kelayakan)</li>
                                    <li><strong>Activation Function:</strong> Sigmoid</li>
                                    <li><strong>Learning Rate:</strong> 0.01</li>
                                </ul>
                            </div>
                        </div>

                        <div class="card mb-3">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-database"></i> Data Training
                                </h6>
                                <p class="mb-2">
                                    Model dilatih menggunakan data historis yang mencakup:
                                </p>
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul class="small">
                                            <li>Data demografis nasabah</li>
                                            <li>Informasi pekerjaan</li>
                                            <li>Riwayat kredit</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul class="small">
                                            <li>Data keuangan</li>
                                            <li>Detail aplikasi kredit</li>
                                            <li>Status kelayakan aktual</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <form method="POST" id="trainForm">
                            <div class="alert alert-info" role="alert">
                                <h6><i class="fas fa-info-circle"></i> Informasi Training</h6>
                                <ul class="mb-0">
                                    <li>Proses training akan memakan waktu beberapa menit</li>
                                    <li>Model akan dilatih dengan 1000 epochs</li>
                                    <li>Progress akan ditampilkan secara real-time</li>
                                    <li>Model yang sudah dilatih akan disimpan otomatis</li>
                                </ul>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-success btn-lg" id="trainBtn">
                                    <i class="fas fa-play"></i> Mulai Training Model
                                </button>
                                <a href="{{ url_for('index') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Kembali ke Beranda
                                </a>
                            </div>
                        </form>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-bar"></i> Statistik Model
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <small class="text-muted">Algoritma</small>
                                    <div class="fw-bold">Backpropagation</div>
                                </div>
                                
                                <div class="mb-3">
                                    <small class="text-muted">Epochs</small>
                                    <div class="fw-bold">1,000</div>
                                </div>
                                
                                <div class="mb-3">
                                    <small class="text-muted">Learning Rate</small>
                                    <div class="fw-bold">0.01</div>
                                </div>
                                
                                <div class="mb-3">
                                    <small class="text-muted">Loss Function</small>
                                    <div class="fw-bold">Binary Cross-Entropy</div>
                                </div>
                                
                                {% if accuracy %}
                                <div class="mb-3">
                                    <small class="text-muted">Akurasi Terakhir</small>
                                    <div class="fw-bold text-success">{{ "%.2f"|format(accuracy * 100) }}%</div>
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="card mt-3">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0">
                                    <i class="fas fa-lightbulb"></i> Tips
                                </h6>
                            </div>
                            <div class="card-body">
                                <small>
                                    <ul class="mb-0">
                                        <li>Training ulang model secara berkala untuk meningkatkan akurasi</li>
                                        <li>Tambahkan data baru untuk hasil yang lebih baik</li>
                                        <li>Monitor performa model secara rutin</li>
                                    </ul>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Progress Section (Hidden by default) -->
        <div class="card mt-4" id="progressCard" style="display: none;">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-spinner fa-spin"></i> Progress Training
                </h5>
            </div>
            <div class="card-body">
                <div class="progress mb-3" style="height: 25px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                         role="progressbar" 
                         style="width: 0%" 
                         id="progressBar">
                        0%
                    </div>
                </div>
                <div id="progressText" class="text-center">
                    Memulai training...
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.getElementById('trainForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Show progress card
    document.getElementById('progressCard').style.display = 'block';
    
    // Disable button
    const trainBtn = document.getElementById('trainBtn');
    trainBtn.disabled = true;
    trainBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Training...';
    
    // Simulate progress (since we can't get real-time progress from Flask easily)
    let progress = 0;
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    
    const interval = setInterval(function() {
        progress += Math.random() * 5;
        if (progress > 95) progress = 95;
        
        progressBar.style.width = progress + '%';
        progressBar.textContent = Math.round(progress) + '%';
        progressText.textContent = `Training epoch ${Math.round(progress * 10)}...`;
    }, 200);
    
    // Submit form
    fetch('/train', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        }
    })
    .then(response => response.text())
    .then(html => {
        clearInterval(interval);
        progressBar.style.width = '100%';
        progressBar.textContent = '100%';
        progressText.textContent = 'Training selesai!';
        
        setTimeout(function() {
            document.body.innerHTML = html;
        }, 1000);
    })
    .catch(error => {
        clearInterval(interval);
        alert('Error: ' + error);
        trainBtn.disabled = false;
        trainBtn.innerHTML = '<i class="fas fa-play"></i> Mulai Training Model';
        document.getElementById('progressCard').style.display = 'none';
    });
});
</script>
{% endblock %}
