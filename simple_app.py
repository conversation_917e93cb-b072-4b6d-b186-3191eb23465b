#!/usr/bin/env python3
"""
Simple Flask app untuk testing
"""

from flask import Flask, render_template_string

app = Flask(__name__)

@app.route('/')
def index():
    return render_template_string('''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Sistem Prediksi Kelayakan Kredit</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    </head>
    <body>
        <div class="container mt-5">
            <div class="row">
                <div class="col-md-8 mx-auto">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h3 class="mb-0">🧠 Sistem Prediksi Kelayakan Kredit</h3>
                        </div>
                        <div class="card-body">
                            <h5>Neural Network dengan Backpropagation</h5>
                            <p>Sistem berhasil dijalankan! 🎉</p>
                            
                            <div class="alert alert-success">
                                <h6>✅ Status Sistem:</h6>
                                <ul class="mb-0">
                                    <li>Flask Server: Running</li>
                                    <li>Database: Ready</li>
                                    <li>Neural Network: Ready</li>
                                    <li>Web Interface: Active</li>
                                </ul>
                            </div>
                            
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <div class="card border-primary">
                                        <div class="card-body text-center">
                                            <h6>📊 Fitur Utama</h6>
                                            <ul class="list-unstyled">
                                                <li>✓ Prediksi Kelayakan Kredit</li>
                                                <li>✓ Training Model AI</li>
                                                <li>✓ Database Management</li>
                                                <li>✓ Web Interface</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-success">
                                        <div class="card-body text-center">
                                            <h6>🔧 Teknologi</h6>
                                            <ul class="list-unstyled">
                                                <li>✓ Python Flask</li>
                                                <li>✓ Neural Network</li>
                                                <li>✓ SQLite Database</li>
                                                <li>✓ Bootstrap UI</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-4 text-center">
                                <a href="/test" class="btn btn-primary">Test Neural Network</a>
                                <a href="/database" class="btn btn-success">Test Database</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    ''')

@app.route('/test')
def test_neural_network():
    try:
        import numpy as np
        from models.neural_network import NeuralNetwork
        
        # Test neural network
        nn = NeuralNetwork(input_size=5, hidden_layers=[3, 2])
        X_test = np.random.rand(10, 5)
        y_test = np.random.randint(0, 2, (10, 1))
        
        # Quick training
        losses = nn.train(X_test, y_test, epochs=50, verbose=False)
        accuracy = nn.calculate_accuracy(X_test, y_test)
        
        return render_template_string('''
        <div class="container mt-5">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4>✅ Neural Network Test - SUCCESS</h4>
                </div>
                <div class="card-body">
                    <p><strong>Test Results:</strong></p>
                    <ul>
                        <li>Neural Network created successfully</li>
                        <li>Training completed: 50 epochs</li>
                        <li>Final accuracy: {{ "%.2f"|format(accuracy * 100) }}%</li>
                        <li>Final loss: {{ "%.4f"|format(losses[-1]) }}</li>
                    </ul>
                    <a href="/" class="btn btn-primary">Back to Home</a>
                </div>
            </div>
        </div>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        ''', accuracy=accuracy, losses=losses)
        
    except Exception as e:
        return render_template_string('''
        <div class="container mt-5">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h4>❌ Neural Network Test - FAILED</h4>
                </div>
                <div class="card-body">
                    <p><strong>Error:</strong> {{ error }}</p>
                    <a href="/" class="btn btn-primary">Back to Home</a>
                </div>
            </div>
        </div>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        ''', error=str(e))

@app.route('/database')
def test_database():
    try:
        from models.database import CreditDatabase
        
        # Test database
        db = CreditDatabase('data/test_credit.db')
        stats = db.get_statistics()
        
        return render_template_string('''
        <div class="container mt-5">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4>✅ Database Test - SUCCESS</h4>
                </div>
                <div class="card-body">
                    <p><strong>Database Statistics:</strong></p>
                    <ul>
                        <li>Total Applications: {{ stats.total_applications }}</li>
                        <li>Approved: {{ stats.approved }}</li>
                        <li>Rejected: {{ stats.rejected }}</li>
                        <li>Total Predictions: {{ stats.total_predictions }}</li>
                    </ul>
                    <a href="/" class="btn btn-primary">Back to Home</a>
                </div>
            </div>
        </div>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        ''', stats=stats)
        
    except Exception as e:
        return render_template_string('''
        <div class="container mt-5">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h4>❌ Database Test - FAILED</h4>
                </div>
                <div class="card-body">
                    <p><strong>Error:</strong> {{ error }}</p>
                    <a href="/" class="btn btn-primary">Back to Home</a>
                </div>
            </div>
        </div>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        ''', error=str(e))

if __name__ == '__main__':
    print("🚀 Starting Simple Credit Prediction System...")
    print("📍 Server will run on: http://localhost:5000")
    print("🔧 This is a test version to verify all components work")
    print("-" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
