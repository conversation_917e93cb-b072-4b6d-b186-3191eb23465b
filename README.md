# 🧠 Sistem Prediksi Kelayakan Kredit

Sistem prediksi kelayakan pemberian kredit menggunakan algoritma **Neural Network dengan Backpropagation**.

## 🎯 Fitur Utama

- ✅ **Prediksi Kelayakan Kredit** - Analisis mendalam profil nasabah
- ✅ **Neural Network Training** - Model AI yang dapat dilatih ulang
- ✅ **Database Management** - Penyimpanan data aplikasi dan prediksi
- ✅ **Web Interface** - Antarmuka web yang user-friendly
- ✅ **API Endpoints** - RESTful API untuk integrasi

## 🏗️ Arsitektur Sistem

### Neural Network
- **Input Layer**: 11 neuron (fitur nasabah)
- **Hidden Layer 1**: 10 neuron
- **Hidden Layer 2**: 8 neuron  
- **Output Layer**: 1 neuron (probabilitas kelayakan)
- **Activation Function**: Sigmoid
- **Learning Algorithm**: Backpropagation

### Parameter Analisis
1. **Umur** - Usia nasabah
2. **Pendapatan Bulanan** - Penghasilan per bulan
3. **Jumlah Tanggungan** - Anggota keluarga yang ditanggung
4. **Lama Bekerja** - Pengalaman kerja dalam tahun
5. **Jumlah Kredit** - Nominal kredit yang diajukan
6. **Jangka Waktu** - Periode pembayaran kredit
7. **Riwayat Kredit** - Track record kredit sebelumnya
8. **Status Pernikahan** - Status perkawinan
9. **Tingkat Pendidikan** - Level pendidikan terakhir
10. **Status Pekerjaan** - Tetap atau tidak tetap
11. **Kepemilikan Rumah** - Milik sendiri atau sewa

## 📁 Struktur Proyek

```
prediksi_kelayakan/
├── app.py                 # Main Flask application
├── config.py              # Konfigurasi sistem
├── requirements.txt       # Dependencies Python
├── demo.py               # Demo dan testing
├── setup_and_run.py      # Setup otomatis
├── models/
│   ├── __init__.py
│   ├── neural_network.py  # Implementasi Neural Network
│   └── database.py        # Database operations
├── templates/
│   ├── base.html          # Template dasar
│   ├── index.html         # Homepage
│   ├── predict.html       # Form prediksi
│   ├── result.html        # Hasil prediksi
│   └── train.html         # Training model
├── data/
│   ├── credit_database.db # Database SQLite
│   └── trained_model.pkl  # Model yang sudah dilatih
└── static/               # CSS, JS, images
```

## 🚀 Cara Menjalankan

### 1. Install Dependencies
```bash
pip install Flask numpy pandas scikit-learn matplotlib seaborn Werkzeug
```

### 2. Jalankan Demo (Opsional)
```bash
python demo.py
```

### 3. Jalankan Aplikasi Web
```bash
python app.py
```

### 4. Akses Aplikasi
Buka browser dan kunjungi: **http://localhost:5000**

## 🎮 Cara Penggunaan

### 1. Prediksi Kelayakan
1. Klik menu **"Prediksi"**
2. Isi form dengan data nasabah
3. Klik **"Prediksi Kelayakan"**
4. Lihat hasil prediksi dan rekomendasi

### 2. Training Model
1. Klik menu **"Training Model"**
2. Klik **"Mulai Training Model"**
3. Tunggu proses training selesai
4. Model baru akan disimpan otomatis

### 3. API Usage
```python
import requests

# Prediksi via API
data = {
    "umur": 35,
    "pendapatan_bulanan": 8000000,
    "jumlah_tanggungan": 2,
    "lama_bekerja": 8,
    "jumlah_kredit": 50000000,
    "jangka_waktu": 36,
    "riwayat_kredit": 1,
    "status_pernikahan": 1,
    "pendidikan": 3,
    "pekerjaan": 1,
    "kepemilikan_rumah": 1
}

response = requests.post('http://localhost:5000/api/predict', json=data)
result = response.json()
print(f"Kelayakan: {result['status']}")
print(f"Probabilitas: {result['probability']:.2%}")
```

## 📊 Contoh Hasil Prediksi

### Nasabah Layak ✅
- **Probabilitas**: 85.3%
- **Status**: LAYAK
- **Confidence**: 85.3%
- **Rekomendasi**: Kredit dapat disetujui

### Nasabah Tidak Layak ❌
- **Probabilitas**: 23.7%
- **Status**: TIDAK LAYAK  
- **Confidence**: 76.3%
- **Rekomendasi**: Kredit sebaiknya ditolak

## 🔧 Konfigurasi

Edit file `config.py` untuk mengubah parameter:

```python
class Config:
    # Neural Network Configuration
    HIDDEN_LAYERS = [10, 8]    # Ukuran hidden layers
    LEARNING_RATE = 0.01       # Learning rate
    EPOCHS = 1000              # Jumlah epoch training
    
    # Database Configuration
    DATABASE_PATH = 'data/credit_database.db'
    MODEL_PATH = 'data/trained_model.pkl'
```

## 📈 Performa Model

- **Akurasi Training**: ~85-95%
- **Waktu Training**: 2-5 menit (1000 epochs)
- **Waktu Prediksi**: <1 detik
- **Data Training**: 1000+ sample otomatis

## 🛠️ Troubleshooting

### Error: Module not found
```bash
pip install -r requirements.txt
```

### Error: Database tidak bisa dibuat
- Pastikan folder `data/` ada
- Periksa permission write

### Error: Model training gagal
- Cek apakah data training tersedia
- Pastikan memory cukup

### Port 5000 sudah digunakan
Edit `app.py` dan ubah port:
```python
app.run(debug=True, host='0.0.0.0', port=5001)
```

## 📝 Catatan Penting

1. **Disclaimer**: Hasil prediksi adalah rekomendasi berdasarkan algoritma machine learning. Keputusan akhir tetap berada di tangan pihak yang berwenang.

2. **Data Privacy**: Pastikan data nasabah dikelola sesuai regulasi yang berlaku.

3. **Model Update**: Lakukan training ulang secara berkala dengan data terbaru untuk meningkatkan akurasi.

4. **Backup**: Backup database dan model secara rutin.

## 🤝 Kontribusi

Sistem ini dapat dikembangkan lebih lanjut dengan:
- Menambah parameter analisis
- Implementasi algoritma ML lain
- Integrasi dengan sistem core banking
- Dashboard analytics
- Export report

## 📞 Support

Jika mengalami masalah atau butuh bantuan:
1. Cek file log untuk error details
2. Pastikan semua dependencies terinstall
3. Restart aplikasi jika diperlukan

---

**© 2024 Sistem Prediksi Kelayakan Kredit - Neural Network dengan Backpropagation**
