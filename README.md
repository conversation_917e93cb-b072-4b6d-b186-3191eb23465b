# 🧠 Sistem Prediksi Kelayakan Kredit dengan Login & Dashboard

Sistem prediksi kelayakan pemberian kredit menggunakan algoritma **Neural Network dengan Backpropagation** yang dilengkapi dengan sistem authentication, dashboard, dan role-based access control.

## 🎯 Fitur Utama

### 🔐 Authentication & Security
- ✅ **Login System** - Sistem login dengan username/password
- ✅ **Role-Based Access** - 3 level akses (Admin, Manager, User)
- ✅ **Session Management** - Pengelolaan sesi user yang aman
- ✅ **Activity Logging** - Pencatatan semua aktivitas user

### 📊 Dashboard & Interface
- ✅ **Modern Dashboard** - Interface yang responsive dan user-friendly
- ✅ **Real-time Statistics** - Statistik sistem dan prediksi
- ✅ **User Profile** - Halaman profil dan pengaturan user
- ✅ **Navigation System** - Navigasi yang intuitif berdasarkan role

### 🧠 AI & Prediction
- ✅ **Neural Network** - Algoritma backpropagation untuk prediksi
- ✅ **Model Training** - Training ulang model (Manager/Admin only)
- ✅ **Prediction Form** - Form input yang comprehensive dengan validasi
- ✅ **Result Analysis** - Analisis hasil dengan rekomendasi bisnis

### 💾 Data Management
- ✅ **Database Integration** - SQLite untuk data dan user management
- ✅ **Auto Data Generation** - Sample data otomatis untuk training
- ✅ **Data Validation** - Validasi input yang ketat
- ✅ **Export/Print** - Fitur export dan print hasil prediksi

## 🏗️ Arsitektur Sistem

### Neural Network
- **Input Layer**: 11 neuron (fitur nasabah)
- **Hidden Layer 1**: 10 neuron
- **Hidden Layer 2**: 8 neuron
- **Output Layer**: 1 neuron (probabilitas kelayakan)
- **Activation Function**: Sigmoid
- **Learning Algorithm**: Backpropagation

### Parameter Analisis
1. **Umur** - Usia nasabah
2. **Pendapatan Bulanan** - Penghasilan per bulan
3. **Jumlah Tanggungan** - Anggota keluarga yang ditanggung
4. **Lama Bekerja** - Pengalaman kerja dalam tahun
5. **Jumlah Kredit** - Nominal kredit yang diajukan
6. **Jangka Waktu** - Periode pembayaran kredit
7. **Riwayat Kredit** - Track record kredit sebelumnya
8. **Status Pernikahan** - Status perkawinan
9. **Tingkat Pendidikan** - Level pendidikan terakhir
10. **Status Pekerjaan** - Tetap atau tidak tetap
11. **Kepemilikan Rumah** - Milik sendiri atau sewa

## 📁 Struktur Proyek

```
prediksi_kelayakan/
├── app.py                 # Main Flask application
├── config.py              # Konfigurasi sistem
├── requirements.txt       # Dependencies Python
├── demo.py               # Demo dan testing
├── setup_and_run.py      # Setup otomatis
├── models/
│   ├── __init__.py
│   ├── neural_network.py  # Implementasi Neural Network
│   └── database.py        # Database operations
├── templates/
│   ├── base.html          # Template dasar
│   ├── index.html         # Homepage
│   ├── predict.html       # Form prediksi
│   ├── result.html        # Hasil prediksi
│   └── train.html         # Training model
├── data/
│   ├── credit_database.db # Database SQLite
│   └── trained_model.pkl  # Model yang sudah dilatih
└── static/               # CSS, JS, images
```

## 🚀 Cara Menjalankan

### 1. Install Dependencies
```bash
pip install Flask Flask-Login Flask-WTF WTForms numpy pandas scikit-learn matplotlib seaborn Werkzeug bcrypt
```

### 2. Jalankan Aplikasi dengan Authentication
```bash
python app_with_auth.py
```

### 3. Akses Aplikasi
Buka browser dan kunjungi: **http://localhost:5000**

### 4. Login dengan Default Users
- **Administrator**: `admin` / `admin123`
- **Credit Manager**: `manager` / `manager123`
- **Credit Analyst**: `user` / `user123`

## 🎮 Cara Penggunaan

### 1. Login ke Sistem
1. Buka **http://localhost:5000**
2. Masukkan username dan password
3. Klik **"Login"**
4. Anda akan diarahkan ke Dashboard

### 2. Dashboard Utama
- **Statistik Real-time** - Lihat statistik aplikasi kredit
- **Quick Actions** - Akses cepat ke fitur utama
- **User Info** - Informasi user dan role
- **Navigation** - Menu navigasi berdasarkan hak akses

### 3. Membuat Prediksi Kelayakan
1. Klik **"Prediksi Kredit"** di sidebar
2. Isi form dengan data nasabah lengkap
3. Sistem akan menampilkan analisis debt-to-income ratio
4. Klik **"Prediksi Kelayakan"**
5. Lihat hasil prediksi dengan rekomendasi bisnis

### 4. Training Model (Manager/Admin)
1. Klik **"Training Model"** di sidebar
2. Klik **"Mulai Training Model"**
3. Tunggu proses training selesai
4. Model baru akan disimpan otomatis

### 5. Manajemen User (Admin Only)
1. Klik **"Kelola User"** di sidebar
2. Lihat daftar semua user
3. Tambah, edit, atau nonaktifkan user
4. Ubah role user sesuai kebutuhan

### 6. Role-Based Features

#### 👤 **Credit Analyst (User)**
- ✅ Melihat dashboard
- ✅ Membuat prediksi kredit
- ✅ Melihat hasil prediksi
- ✅ Mengubah profil dan password

#### 👨‍💼 **Credit Manager**
- ✅ Semua fitur Credit Analyst
- ✅ Training model neural network
- ✅ Melihat statistik lanjutan
- ✅ Monitoring performa model

#### 👨‍💻 **Administrator**
- ✅ Semua fitur Credit Manager
- ✅ Mengelola user (CRUD)
- ✅ Melihat analytics lengkap
- ✅ Mengubah role user
- ✅ Mendaftarkan user baru

## 📊 Contoh Hasil Prediksi

### Nasabah Layak ✅
- **Probabilitas**: 85.3%
- **Status**: LAYAK
- **Confidence**: 85.3%
- **Rekomendasi**: Kredit dapat disetujui

### Nasabah Tidak Layak ❌
- **Probabilitas**: 23.7%
- **Status**: TIDAK LAYAK
- **Confidence**: 76.3%
- **Rekomendasi**: Kredit sebaiknya ditolak

## 🔧 Konfigurasi

Edit file `config.py` untuk mengubah parameter:

```python
class Config:
    # Neural Network Configuration
    HIDDEN_LAYERS = [10, 8]    # Ukuran hidden layers
    LEARNING_RATE = 0.01       # Learning rate
    EPOCHS = 1000              # Jumlah epoch training

    # Database Configuration
    DATABASE_PATH = 'data/credit_database.db'
    MODEL_PATH = 'data/trained_model.pkl'
```

## 📈 Performa Model

- **Akurasi Training**: ~85-95%
- **Waktu Training**: 2-5 menit (1000 epochs)
- **Waktu Prediksi**: <1 detik
- **Data Training**: 1000+ sample otomatis

## 🛠️ Troubleshooting

### Error: Module not found
```bash
pip install -r requirements.txt
```

### Error: Database tidak bisa dibuat
- Pastikan folder `data/` ada
- Periksa permission write

### Error: Model training gagal
- Cek apakah data training tersedia
- Pastikan memory cukup

### Port 5000 sudah digunakan
Edit `app.py` dan ubah port:
```python
app.run(debug=True, host='0.0.0.0', port=5001)
```

## 📝 Catatan Penting

1. **Disclaimer**: Hasil prediksi adalah rekomendasi berdasarkan algoritma machine learning. Keputusan akhir tetap berada di tangan pihak yang berwenang.

2. **Data Privacy**: Pastikan data nasabah dikelola sesuai regulasi yang berlaku.

3. **Model Update**: Lakukan training ulang secara berkala dengan data terbaru untuk meningkatkan akurasi.

4. **Backup**: Backup database dan model secara rutin.

## 🤝 Kontribusi

Sistem ini dapat dikembangkan lebih lanjut dengan:
- Menambah parameter analisis
- Implementasi algoritma ML lain
- Integrasi dengan sistem core banking
- Dashboard analytics
- Export report

## 📞 Support

Jika mengalami masalah atau butuh bantuan:
1. Cek file log untuk error details
2. Pastikan semua dependencies terinstall
3. Restart aplikasi jika diperlukan

---

**© 2024 Sistem Prediksi Kelayakan Kredit - Neural Network dengan Backpropagation**
