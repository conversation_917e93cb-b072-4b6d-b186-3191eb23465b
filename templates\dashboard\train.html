{% extends "dashboard/base.html" %}

{% block title %}Training Model Neural Network{% endblock %}
{% block page_title %}Training Model Neural Network{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-10 mx-auto">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-brain me-2"></i>
                    Training Model Neural Network
                </h4>
            </div>
            <div class="card-body">
                {% if success %}
                <div class="alert alert-success" role="alert">
                    <h5><i class="fas fa-check-circle me-2"></i>Training Berhasil!</h5>
                    <p class="mb-2">{{ message }}</p>
                    <p class="mb-0"><strong>Akurasi Model: {{ "%.2f"|format(accuracy * 100) }}%</strong></p>
                </div>
                {% endif %}

                {% if error %}
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>{{ error }}
                </div>
                {% endif %}

                <div class="row">
                    <div class="col-md-8">
                        <h5 class="text-primary mb-3">
                            <i class="fas fa-cogs me-2"></i>
                            Proses Training Model
                        </h5>
                        
                        <p>
                            Sistem menggunakan algoritma <strong>Neural Network dengan Backpropagation</strong> 
                            untuk mempelajari pola dari data historis aplikasi kredit.
                        </p>

                        <div class="card mb-3">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-network-wired me-2"></i>
                                    Arsitektur Neural Network
                                </h6>
                                <ul class="mb-0">
                                    <li><strong>Input Layer:</strong> 11 neuron (sesuai jumlah fitur)</li>
                                    <li><strong>Hidden Layer 1:</strong> 10 neuron</li>
                                    <li><strong>Hidden Layer 2:</strong> 8 neuron</li>
                                    <li><strong>Output Layer:</strong> 1 neuron (probabilitas kelayakan)</li>
                                    <li><strong>Activation Function:</strong> Sigmoid</li>
                                    <li><strong>Learning Rate:</strong> 0.01</li>
                                </ul>
                            </div>
                        </div>

                        <div class="card mb-3">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-database me-2"></i>
                                    Data Training
                                </h6>
                                <p class="mb-2">
                                    Model dilatih menggunakan data historis yang mencakup:
                                </p>
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul class="small">
                                            <li>Data demografis nasabah</li>
                                            <li>Informasi pekerjaan</li>
                                            <li>Riwayat kredit</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul class="small">
                                            <li>Data keuangan</li>
                                            <li>Detail aplikasi kredit</li>
                                            <li>Status kelayakan aktual</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <form method="POST" id="trainForm">
                            <div class="alert alert-info" role="alert">
                                <h6><i class="fas fa-info-circle me-2"></i>Informasi Training</h6>
                                <ul class="mb-0">
                                    <li>Proses training akan memakan waktu beberapa menit</li>
                                    <li>Model akan dilatih dengan 200 epochs (demo)</li>
                                    <li>Progress akan ditampilkan di console</li>
                                    <li>Model yang sudah dilatih akan disimpan otomatis</li>
                                </ul>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-success btn-lg" id="trainBtn">
                                    <i class="fas fa-play me-2"></i>
                                    Mulai Training Model
                                </button>
                                <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    Kembali ke Dashboard
                                </a>
                            </div>
                        </form>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    Statistik Model
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <small class="text-muted">Algoritma</small>
                                    <div class="fw-bold">Backpropagation</div>
                                </div>
                                
                                <div class="mb-3">
                                    <small class="text-muted">Epochs</small>
                                    <div class="fw-bold">200 (Demo)</div>
                                </div>
                                
                                <div class="mb-3">
                                    <small class="text-muted">Learning Rate</small>
                                    <div class="fw-bold">0.01</div>
                                </div>
                                
                                <div class="mb-3">
                                    <small class="text-muted">Loss Function</small>
                                    <div class="fw-bold">Binary Cross-Entropy</div>
                                </div>
                                
                                {% if accuracy %}
                                <div class="mb-3">
                                    <small class="text-muted">Akurasi Terakhir</small>
                                    <div class="fw-bold text-success">{{ "%.2f"|format(accuracy * 100) }}%</div>
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="card mt-3">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0">
                                    <i class="fas fa-lightbulb me-2"></i>
                                    Tips Training
                                </h6>
                            </div>
                            <div class="card-body">
                                <small>
                                    <ul class="mb-0">
                                        <li>Training ulang model secara berkala untuk meningkatkan akurasi</li>
                                        <li>Tambahkan data baru untuk hasil yang lebih baik</li>
                                        <li>Monitor performa model secara rutin</li>
                                        <li>Backup model sebelum training ulang</li>
                                    </ul>
                                </small>
                            </div>
                        </div>
                        
                        <div class="card mt-3">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-user-shield me-2"></i>
                                    Akses Training
                                </h6>
                            </div>
                            <div class="card-body">
                                <small>
                                    <p class="mb-2">Fitur training model hanya tersedia untuk:</p>
                                    <ul class="mb-0">
                                        <li><i class="fas fa-check text-success me-1"></i> Credit Manager</li>
                                        <li><i class="fas fa-check text-success me-1"></i> Administrator</li>
                                    </ul>
                                    <p class="mt-2 mb-0 text-muted">
                                        Role Anda: <span class="badge bg-primary">{{ current_user.role.title() }}</span>
                                    </p>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Progress Section (Hidden by default) -->
        <div class="card mt-4" id="progressCard" style="display: none;">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-spinner fa-spin me-2"></i>
                    Progress Training
                </h5>
            </div>
            <div class="card-body">
                <div class="progress mb-3" style="height: 25px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                         role="progressbar" 
                         style="width: 0%" 
                         id="progressBar">
                        0%
                    </div>
                </div>
                <div id="progressText" class="text-center">
                    Memulai training...
                </div>
                <div class="text-center mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Training sedang berjalan. Jangan tutup halaman ini.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.getElementById('trainForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Show progress card
    document.getElementById('progressCard').style.display = 'block';
    
    // Disable button
    const trainBtn = document.getElementById('trainBtn');
    trainBtn.disabled = true;
    trainBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Training...';
    
    // Simulate progress
    let progress = 0;
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    
    const interval = setInterval(function() {
        progress += Math.random() * 3;
        if (progress > 95) progress = 95;
        
        progressBar.style.width = progress + '%';
        progressBar.textContent = Math.round(progress) + '%';
        progressText.textContent = `Training epoch ${Math.round(progress * 2)}...`;
    }, 500);
    
    // Submit form
    fetch('/train', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        }
    })
    .then(response => response.text())
    .then(html => {
        clearInterval(interval);
        progressBar.style.width = '100%';
        progressBar.textContent = '100%';
        progressText.textContent = 'Training selesai!';
        
        setTimeout(function() {
            location.reload();
        }, 1000);
    })
    .catch(error => {
        clearInterval(interval);
        alert('Error: ' + error);
        trainBtn.disabled = false;
        trainBtn.innerHTML = '<i class="fas fa-play me-2"></i>Mulai Training Model';
        document.getElementById('progressCard').style.display = 'none';
    });
});
</script>
{% endblock %}
