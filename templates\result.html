{% extends "base.html" %}

{% block title %}Hasil <PERSON>diksi - {{ result.nama }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <!-- Hasil Prediksi -->
        <div class="card mb-4">
            <div class="card-header {% if result.predicted_class == 1 %}bg-success{% else %}bg-danger{% endif %} text-white">
                <h4 class="mb-0">
                    <i class="fas {% if result.predicted_class == 1 %}fa-check-circle{% else %}fa-times-circle{% endif %}"></i>
                    Hasil Prediksi Kelayakan Kredit
                </h4>
            </div>
            <div class="card-body text-center">
                <h2 class="mb-3">{{ result.nama }}</h2>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="card {% if result.predicted_class == 1 %}border-success{% else %}border-danger{% endif %}">
                            <div class="card-body">
                                <h5 class="card-title">Status Kelayakan</h5>
                                <h3 class="{% if result.predicted_class == 1 %}status-layak{% else %}status-tidak-layak{% endif %}">
                                    {{ result.status }}
                                </h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-info">
                            <div class="card-body">
                                <h5 class="card-title">Tingkat Keyakinan</h5>
                                <h3 class="text-info">{{ result.confidence }}</h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="mt-4">
                    <label class="form-label">Skor Probabilitas: {{ "%.1f"|format(result.probability * 100) }}%</label>
                    <div class="progress" style="height: 25px;">
                        <div class="progress-bar {% if result.predicted_class == 1 %}bg-success{% else %}bg-danger{% endif %}" 
                             role="progressbar" 
                             style="width: {{ result.probability * 100 }}%"
                             aria-valuenow="{{ result.probability * 100 }}" 
                             aria-valuemin="0" 
                             aria-valuemax="100">
                            {{ "%.1f"|format(result.probability * 100) }}%
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Interpretasi Hasil -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb"></i> Interpretasi Hasil
                </h5>
            </div>
            <div class="card-body">
                {% if result.predicted_class == 1 %}
                <div class="alert alert-success" role="alert">
                    <h6><i class="fas fa-check-circle"></i> Kredit LAYAK untuk disetujui</h6>
                    <p class="mb-0">
                        Berdasarkan analisis neural network, nasabah memiliki profil yang baik untuk mendapatkan kredit. 
                        Sistem menilai bahwa risiko kredit macet relatif rendah dengan tingkat keyakinan {{ result.confidence }}.
                    </p>
                </div>
                
                <h6>Rekomendasi:</h6>
                <ul>
                    <li>Proses aplikasi kredit dapat dilanjutkan</li>
                    <li>Lakukan verifikasi dokumen pendukung</li>
                    <li>Pertimbangkan untuk memberikan limit kredit sesuai kemampuan</li>
                    {% if result.probability < 0.75 %}
                    <li>Monitoring ekstra selama masa awal kredit (probabilitas di bawah 75%)</li>
                    {% endif %}
                </ul>
                {% else %}
                <div class="alert alert-danger" role="alert">
                    <h6><i class="fas fa-times-circle"></i> Kredit TIDAK LAYAK untuk disetujui</h6>
                    <p class="mb-0">
                        Berdasarkan analisis neural network, nasabah memiliki profil berisiko tinggi. 
                        Sistem menilai bahwa kemungkinan kredit macet cukup tinggi dengan tingkat keyakinan {{ result.confidence }}.
                    </p>
                </div>
                
                <h6>Rekomendasi:</h6>
                <ul>
                    <li>Aplikasi kredit sebaiknya ditolak atau ditunda</li>
                    <li>Sarankan nasabah untuk memperbaiki profil keuangan</li>
                    <li>Pertimbangkan kredit dengan jumlah lebih kecil atau jaminan tambahan</li>
                    <li>Evaluasi ulang setelah 6-12 bulan</li>
                </ul>
                {% endif %}
            </div>
        </div>

        <!-- Faktor Penting -->
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle"></i> Catatan Penting
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning" role="alert">
                    <strong>Disclaimer:</strong> Hasil prediksi ini adalah rekomendasi berdasarkan analisis algoritma machine learning. 
                    Keputusan akhir tetap berada di tangan pihak yang berwenang dengan mempertimbangkan faktor-faktor lain 
                    yang mungkin tidak tercakup dalam model ini.
                </div>
                
                <h6>Faktor yang Mempengaruhi Keputusan:</h6>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-money-bill text-success"></i> Rasio pendapatan vs kredit
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-history text-primary"></i> Riwayat kredit sebelumnya
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-briefcase text-warning"></i> Stabilitas pekerjaan
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-user text-info"></i> Profil demografis
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-home text-secondary"></i> Aset dan jaminan
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-users text-danger"></i> Beban tanggungan
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="d-grid gap-2 d-md-flex justify-content-md-center">
            <a href="{{ url_for('predict') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-plus"></i> Prediksi Baru
            </a>
            <a href="{{ url_for('index') }}" class="btn btn-secondary btn-lg">
                <i class="fas fa-home"></i> Kembali ke Beranda
            </a>
            <button onclick="window.print()" class="btn btn-info btn-lg">
                <i class="fas fa-print"></i> Cetak Hasil
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Auto-hide alerts after 10 seconds
setTimeout(function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        if (!alert.classList.contains('alert-warning')) {
            alert.style.transition = 'opacity 0.5s';
            alert.style.opacity = '0.8';
        }
    });
}, 10000);

// Print styles
const style = document.createElement('style');
style.textContent = `
    @media print {
        .btn, .card-header { 
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
        }
        .btn { display: none !important; }
        .navbar, .footer { display: none !important; }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
