import os

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-prediksi-kredit'
    DATABASE_PATH = 'data/credit_database.db'
    USER_DATABASE_PATH = 'data/users.db'
    MODEL_PATH = 'data/trained_model.pkl'

    # Neural Network Configuration
    HIDDEN_LAYERS = [10, 8]  # Hidden layer sizes
    LEARNING_RATE = 0.01
    EPOCHS = 1000

    # Database Configuration
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # Authentication Configuration
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = None

    # Session Configuration
    PERMANENT_SESSION_LIFETIME = 3600  # 1 hour
